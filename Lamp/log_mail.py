import inspect
import sys
import threading
import traceback

import requests
from django.conf import settings
from django.core.mail.message import EmailMultiAlternatives
from django.utils.log import AdminEmail<PERSON><PERSON><PERSON>
from requests import RequestException

from Lamp.settings import logger


class MyEmailHandler(AdminEmailHandler):

    def do_send(self, subject, message):
        # 通过api发送error
        try:
            post_data = {
                "msg": message,
                "token": "v-MxkdFATpo-k2_mASaKx_token"
            }
            response = requests.post(
                "https://api.v3phone.xyz/v3sim/config/alertFoError/",
                json=post_data,
                timeout=10  # 添加超时设置
            )
            response.raise_for_status()  # 检查HTTP状态码
            logger.info(f"Error sent to API successfully: {message}")
        except RequestException as e:
            logger.error(f"Network error when sending to API: {e}")
        except Exception as e:
            logger.error(f"Unexpected error when sending to API: {e}", exc_info=True)

        mail = EmailMultiAlternatives(u'%s%s' % (settings.EMAIL_SUBJECT_PREFIX, subject),
                                      message, settings.SERVER_EMAIL, [a[1] for a in settings.ADMINS], )
        mail.send(fail_silently=False)

    def emit(self, record):
        try:
            if not getattr(settings, "ADMINS", None):
                return

            subject = self.format_subject(record.getMessage())
            origin_message = getattr(record, "email_body", record.getMessage())
            exc_info = sys.exc_info()
            message = origin_message + '\n' + ''.join(traceback.format_exception(*exc_info))

            # 带上堆栈，更好观察情况
            message += "\n当前堆栈：\n"
            frames = inspect.stack()
            for f in frames:
                if 'lamp' in f.filename:
                    message += f"{f.filename}\t{f.lineno}\t{f.function}\n"

            # rate受限，不用携带上下文
            if 'ratelimit.exceptions.Ratelimited' in message:
                message = origin_message

            # 异步发送
            t = threading.Thread(target=self.do_send, args=(subject, message))
            t.start()
        except Exception as e:
            logger.info(traceback.format_exc())
