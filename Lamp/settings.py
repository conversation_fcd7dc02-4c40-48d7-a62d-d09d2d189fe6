"""
Django settings for Lamp project.

Generated by 'django-admin startproject' using Django 2.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/2.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.1/ref/settings/
"""
import json
import logging
import os
import sys
import traceback

import environ
from firebase_admin import credentials, initialize_app

env = environ.Env()
environ.Env.read_env()

LOCAL_DEV = False

DEBUG = LOCAL_DEV

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '2xgtu!=qpej@=^$11e=&-(c%=0c75^5jo8e*-zuiv_cw32+qyy'

# SECURITY WARNING: don't run with debug turned on in production!

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'User',
    'Server',
    'Config',
    'Feedback',
    'Order',
    'Tongji',
    'Report',
    'admin_totals',
    'Mogaoku',  ## for personal temp use
    'Sms',
    'ToolsForSms',
    'ToolsForOrder',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'Lamp.logid_middleware.RequestLogMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'Lamp.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')]
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'Lamp.wsgi.application'

# --------------Database-----------------
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env('DB_DATABASE_NAME_KEY'),
        'USER': env('DB_USER_KEY'),
        'PASSWORD': env('DB_PASSWORD_DEV_KEY'),
        'HOST': '127.0.0.1',
        'PORT': env('DB_PORT_KEY'),
        'OPTIONS': {'charset': 'utf8mb4'}
    }
}

if LOCAL_DEV is False:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': env('DB_DATABASE_NAME_KEY'),
            'USER': env('DB_USER_KEY'),
            'PASSWORD': env('DB_PASSWORD_KEY'),
            'HOST': env('DB_HOST_KEY'),
            'PORT': env('DB_PORT_KEY'),
            'OPTIONS': {'charset': 'utf8mb4'},
            "POOL_OPTIONS": {
                "POOL_SIZE": 15,
                "MAX_OVERFLOW": 5,
                "RECYCLE": 10 * 60  # 10 min,
            },
        }
    }

# Password validation
# https://docs.djangoproject.com/en/2.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/2.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.1/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR + '/static'

## -----------sign & salt--------------------------------------------

SIGN_TS_ALLOW_DIFF = 3600 * 24  # 24 小时，防一手不同时区

# password salt
SALT_SECRET_KEY = 'Nzu8HXM8Jvl9V7$ZZ$v3YHl98nT0U&3w5IoFT!K5ubi7'

# sign key
SIGN_SECRET_KEY = '!u2Nr&KKNZvwd&!xsAHy9(Npijed5!$0ZDcAD7Ep)IuV'
SIGN_SECRET_KEY_FOR_ANDROID = 'xQ9!kP3@zF7#hJ2$dK5%vM8&nG1^bR4*yT6(wS0)eL9['

# max device number per account
MAX_DEVICE_NUM = 5

## --------v account----------
SHARE_PASSWORD = 'ImxIxAmNuNUywNsa'

# ---------------------CACHE-------------------
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{env('REDIS_HOST_KEY')}:6379/0",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            "SERIALIZER": "django_redis.serializers.json.JSONSerializer"
        },
        "KEY_FUNCTION": "Lamp.settings.make_key"
    }
}


def make_key(key, key_prefix, version):
    return '%s' % key


DEFAULT_CACHE_TIMEOUT = 3600

##---------server user number to status--------------

IDLE = 5
BUSY = 10

REPORT_KEY = 'XNsj^Nbsj72jAn-xYwuiUKS_xMMZ'

COUNTRY_EN = {
    "DEFAULT": "Optimal",
    "US": "United States",
    "SGP": "Singapore",
    "UK": "United Kingdom",
    "GE": "German",
    "CAN": "Canada",
    "CA": "Canada",
    "IN": "Indian",
    "JP": "Japan",
    "FR": "France",
    "AU": "Australia",
    "BR": "Brazil",
    "SE": "Sweden",
    "NL": "Netherland",
    "SK": "South Korea",
    "MX": "Mexico",
    "HK": "HongKong",
    "GB": "Britain",
}

COUNTRY_RU = {
    "DEFAULT": "Оптимальный",
    "US": "Соединенные Штаты",
    "SGP": "Сингапур",
    "UK": "Великобритания",
    "GE": "Германия",
    "CAN": "Канада",
    "CA": "Канада",
    "IN": "Индия",
    "JP": "Япония",
    "FR": "Франция",
    "AU": "Австралия",
    "BR": "Бразилия",
    "SE": "Швеция",
    "NL": "Нидерланды",
    "SK": "Южная Корея",
    "MX": "Мексика",
    "HK": "Гонконг",
    "GB": "Британия",
}

FLAG_ADDRESS = 'https://api.gongbaojiding.xyz/static/flag/'

# -------------- email ----------------

EMAIL_ACCOUNT = '<EMAIL>'
EMAIL_PASSWORD = 'xxxxx'
EMAIL_TEMPLATE = 'Hi there,<br><br>your password reset code is <b>%s</b><br><br>ZeHou Tech'


# ------------------- logging -------------------
class PathTruncatingFormatter(logging.Formatter):
    def format(self, record):
        if 'pathname' in record.__dict__.keys():
            record.pathname = record.pathname.replace(BASE_DIR, '')
        return super(PathTruncatingFormatter, self).format(record)


LOG_FORMAT = "[%(asctime)s][%(levelname)s][%(pathname)s:%(lineno)d] %(message)s"

ch = logging.StreamHandler(sys.stdout)
formatter = PathTruncatingFormatter(LOG_FORMAT)
root = logging.getLogger()
root.setLevel(logging.INFO)
ch.setFormatter(formatter)
root.addHandler(ch)

# ----------------encrypt------------------
AES_KEY = 'MkAdN-sd4ms-11LAqS-Mk0iUn-Bxs8ms'
AES_IV = '9gKs1R0T1sQtPx06'

# ----------------- apple receipt ---------------
APPLE_VERIFY_PASSWORD = 'CHANGE-ME!'

APPLE_VERIFY_URL = 'https://sandbox.itunes.apple.com/verifyReceipt'
if LOCAL_DEV is False:
    APPLE_VERIFY_URL = 'https://buy.itunes.apple.com/verifyReceipt'

# ---------------swan server--------------
SWAN_ACCESS_TOKEN = 'v-MxkdFATpo-k2_mASaKx_token'

# ---------------log--------------
LOG_DIR = os.path.join(BASE_DIR, "logs")
if not os.path.exists(LOG_DIR):
    os.mkdir(LOG_DIR)

##-------------- mail -------------
# 管理员邮箱

ADMINS = (
    ('fatpo', env('EMAIL_FATPO_KEY')),
    # ('chong', env('EMAIL_CHONG_KEY')),
    # ('sjj', env('EMAIL_SJJ_KEY')),
)

TOTAL_ADMINS = (
    ('fatpo', env('EMAIL_FATPO_KEY')),
    ('chong', env('EMAIL_CHONG_KEY')),
    ('sjj', env('EMAIL_SJJ_KEY')),
)

# 开发环境就不要邮件报警
if LOCAL_DEV:
    ADMINS = ()

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.qq.com'  # QQ邮箱SMTP服务器(邮箱需要开通SMTP服务)
EMAIL_PORT = 587  # QQ邮箱SMTP服务端口
EMAIL_HOST_USER = env('EMAIL_FROM_USER_KEY')  # 我的邮箱帐号
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD_KEY')  # 授权码  不是你的QQ密码
EMAIL_SUBJECT_PREFIX = '[GBJD]'  # 为邮件标题的前缀,默认是'[django]'
EMAIL_USE_TLS = True  # 开启安全链接
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL_KEY')
SERVER_EMAIL = env('SERVER_EMAIL_KEY')  # 设置发件人

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '[%(levelname)s][%(asctime)s][%(logid)s][%(filename)s][%(funcName)s][%(lineno)d] > %(message)s' \
                .replace("\n", " ").replace("\r", " ")
        },
        'simple': {
            'format': '[%(levelname)s][%(logid)s] > %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
    },
    'handlers': {
        'console': {
            'level': 'WARNING',
            'filters': ['logid_filter'],
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        },
        'error_file_handler': {
            'level': 'WARNING',
            'filters': ['logid_filter'],
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': '%s/django.err' % LOG_DIR,
            'formatter': 'standard',
            'encoding': 'utf-8'
        },
        'file_handler': {
            'level': 'INFO',
            'filters': ['logid_filter'],
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': '%s/django.log' % LOG_DIR,
            'formatter': 'standard',
            'encoding': 'utf-8'
        },  # 用于文件输出
        'mail_admins_handler': {
            'level': 'ERROR',
            'filters': ['logid_filter'],
            'class': 'Lamp.log_mail.MyEmailHandler',
            'formatter': 'standard',
            'include_html': True,
        },
    },
    'loggers': {
        'mdjango': {
            # 一个记录器中可以使用多个处理器
            'handlers': ['file_handler', 'mail_admins_handler', 'error_file_handler'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'django.request': {
            'handlers': ['mail_admins_handler'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
    'filters': {
        'logid_filter': {
            '()': 'Lamp.logid_middleware.LogidFilter',  # 指定你的过滤器
        },
    },
}

logger = logging.getLogger("mdjango")

# ORDER CONFIG
# 6222605d-63d4-4e1b-b718-74179f561011   ran
WHITE_USER_LIST = ["df5a230c-cfe9-4842-ad75-80b501851b89", "6763dfdb-b2fb-4b38-9d3f-08094af770e9",
                   "6222605d-63d4-4e1b-b718-74179f561011"]

## -------------------apple verify---------------
APPLE_VERIFY_URL = 'https://buy.itunes.apple.com/verifyReceipt'
APPLE_VERIFY_URL_SANDBOX = 'https://sandbox.itunes.apple.com/verifyReceipt'
APPLE_VIP_PRODUCT_ID_COLUMN_NAME = env('APPLE_VIP_PRODUCT_ID_COLUMN_NAME')

APPLE_VERIFY_PASSWORD = {
    0: env('APPLE_VERIFY_PASSWORD'),
}

# app的包名，用于订单验证
APPLE_BUNDLE_ID = {
    0: env('APPLE_BUNDLE_ID'),
}

# URL
STATIC_URL_BASE = 'https://api.gongbaojiding.xyz'

# 客服号码
APP_IT_SUPPORT_SHOW_PHONE = env("APP_IT_SUPPORT_SHOW_PHONE")
APP_IT_SUPPORT_NO_NUMBER_PREFIX = env("APP_IT_SUPPORT_NO_NUMBER_PREFIX")
APP_IT_SUPPORT_DEVICEID = env("APP_IT_SUPPORT_DEVICEID")

# **************** fcm ****************
# ***************************************
FCM_CERT_FILEPATH_FILENAME = env('FCM_CERT_FILEPATH_FILENAME')
FCM_CERT_FILEPATH = BASE_DIR + FCM_CERT_FILEPATH_FILENAME

with open(FCM_CERT_FILEPATH) as f:
    credential = credentials.Certificate(json.load(f))
FCM_APP_NAME = env('FCM_APP_NAME')
try:
    FIREBASE_APP = initialize_app(credential=credential, name=FCM_APP_NAME)
except Exception:
    traceback.print_exc()
    print('fcm init error')

# 短信相关
SMS_DIRECTION_SEND = "SEND"
SMS_DIRECTION_RECEIVE = "RECEIVE"

# VIP
vip_file = BASE_DIR + '/Config/vip.json'
with open(vip_file) as f:
    vip_data = json.loads(f.read())


def extract_product_ids(json_data: dict) -> set:
    product_ids = set()

    for key, value in json_data.items():
        if isinstance(value, dict):
            # 如果值是字典，则递归调用函数
            product_ids.update(extract_product_ids(value))
        elif isinstance(value, list):
            for i in value:
                product_ids.update(extract_product_ids(i))
        elif key == APPLE_VIP_PRODUCT_ID_COLUMN_NAME:
            # 找到目标 product_id，添加到集合中
            product_ids.add(value)

    return product_ids


total_vip_product_ids = extract_product_ids(vip_data)

######################
SESSION_COOKIE_AGE = 3600 * 24 * 30  # admin登录 30 天

####################
SERVERS_MAX_PER_GROUP = 6
SERVERS_MAX_PER_US_CITY = 6
SERVERS_MAX_PER_COUNTRY = 6
