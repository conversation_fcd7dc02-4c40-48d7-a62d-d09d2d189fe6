'''
Created on Oct 6, 2021

@author: jon
'''
from django.conf.urls import url

from Config import views

urlpatterns = [
    url(r'^lpapi/config/$', views.GetConfig.as_view()),
    url(r'^lpapi/config/vip/$', views.GetVipConfig.as_view()),
    url(r'^lpapi/config/checkHealth/$', views.CheckHealth.as_view()),
    url(r'^lpapi/config/receiveServerReport/$', views.ReceiveServerReport.as_view()),
    url(r'^lpapi/config/receiveUserUsageReport/$', views.ReceiveUserUsageReport.as_view()),
    url(r'^lpapi/config/changeClientsIpNotify/$', views.ChangeClientsIpNotify.as_view()),
    url(r'^lpapi/config/changeClientsIpNotify/test/$', views.ChangeClientsIpNotifyTest.as_view()),
]
