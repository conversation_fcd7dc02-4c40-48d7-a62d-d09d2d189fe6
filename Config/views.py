import json
import threading

from Common.timeutil import TimeUtil
from Common.views import CommonView
from Config.models import Config
from Lamp import settings
from Lamp.settings import logger
from Order.models import Order
from Push.push_util import PushUtil
from Server.dao.server_dao import ServerDao
from Server.models import Server
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_sms import SmsTool
from User.dao.user_dao import UserDao
from User.models import UserTraffic, User, UserUsage


class GetVipConfig(CommonView):

    @CommonView.VerifySign
    def post(self, request):
        headers = self.GetHeaderInRequest(request)
        appid = str(headers.get('appid', 0))
        device_id = str(headers.get('device_id', '0'))
        lang = str(headers.get('lang', 'en'))

        config = self.__read_config_file()
        res = config.get(appid, None)

        if res is None:
            res = config.get("0")

        is_ready_ordered_before = False
        if Order.objects.filter(device_id=device_id).first():
            for i in res["list"]:
                i["is_free_trial"] = False
            logger.info(f"[GetVipConfig] device_id:{device_id} already has order, remove is_free_trial, config:{res}")
            is_ready_ordered_before = True

        # 国际化
        try:
            for i in res["list"]:
                if lang == "ru":
                    i["payment"] = i["payment_ru"]
                    i["price_disc"] = i["price_disc_ru"]
                    i["sub_title"] = i["sub_title_ru"]
                    if is_ready_ordered_before:
                        i["sub_title"] = i["sub_title_already_trialed_ru"]
                else:
                    i["payment"] = i["payment_en"]
                    i["price_disc"] = i["price_disc_en"]
                    i["sub_title"] = i["sub_title_en"]
                    if is_ready_ordered_before:
                        i["sub_title"] = i["sub_title_already_trialed_en"]

                if "payment_en" in i:
                    del i["payment_en"]
                if "payment_ru" in i:
                    del i["payment_ru"]
                if "sub_title_en" in i:
                    del i["sub_title_en"]
                if "sub_title_ru" in i:
                    del i["sub_title_ru"]
                if "sub_title_already_trialed_en" in i:
                    del i["sub_title_already_trialed_en"]
                if "sub_title_already_trialed_ru" in i:
                    del i["sub_title_already_trialed_ru"]
                if "price_disc_en" in i:
                    del i["price_disc_en"]
                if "price_disc_ru" in i:
                    del i["price_disc_ru"]

        except Exception:
            logger.error(f"[GetVipConfig] device_id:{device_id}, lang:{lang}, config:{res}", exc_info=True)

        logger.info(f"[GetVipConfig] device_id:{device_id}, lang:{lang}, config:{res}")
        return self.ReturnSuccess(res)

    @staticmethod
    def __read_config_file():
        file = settings.BASE_DIR + '/Config/vip.json'
        with open(file) as f:
            return json.loads(f.read())


class GetConfig(CommonView):

    # GET /config/?channel=xxx&version=1
    @CommonView.VerifySign
    def get(self, request):

        data, err_code, err_msg = self.GetDataInGet(request, ('channel',))
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        version = int(data.get('version', 0))

        logger.info(f"get config, channel={data['channel']}, version={version}")

        config = self.GetOrNone(Config, channel=data['channel'], version=version)

        if config is None:
            version = 0
            config = self.GetOrNone(Config, channel=data['channel'], version=version)

        content = {}
        if config is not None:
            content = json.loads(config.config)

        return self.ReturnSuccess(content)


class CheckHealth(CommonView):

    def get(self, _):
        servers = Server.objects.all()
        logger.info(f"[CheckHealth] servers cnt: {len(servers)}")

        return self.ReturnSuccess()


class ReceiveServerReport(CommonView):

    def post(self, request):
        """
        [{
            'ip': '************',
            'extra_info': {
                'hostname': 'v-us-dfw-182-**********',
                'country': 'US',
                'websites': {
                    'youtube': 'success',
                    'tiktok': 'success',
                    'instagram': 'success',
                    'facebook': 'success'
                }
            },
            'test_result': True
        }, {
            'ip': '************',
            'extra_info': {
                'hostname': 'v-us-dfw-411-**********',
                'country': 'US',
                'websites': {
                    'youtube': 'success',
                    'tiktok': 'success',
                    'instagram': 'success',
                    'facebook': 'success'
                }
            },
            'test_result': True
        }]

        :param request:
        :return:
        """
        post_data, err_code, err_msg = self.ReadPostJson(request, check_list=[])
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)
        logger.info(f"[ReceiveServerReport] receive server report: {post_data}")

        is_alert = False
        need_alert_list = []
        normal_cnt = 0
        for i in post_data:
            ip = i['ip']
            server = ServerDao.get_server_by_ip_without_condition(ip)
            if not server:
                logger.error(f"[ReceiveServerReport] server not found in DB, ip: {ip}, client info: {i}")
                continue

            # 更新 websites 访问状态
            if 'extra_info' in i and 'websites' in i['extra_info']:
                websites = i['extra_info']['websites']
                server.is_access_youtube = 1 if websites.get('youtube', 'failed') == 'success' else 0
                server.is_access_tiktok = 1 if websites.get('tiktok', 'failed') == 'success' else 0
                server.is_access_instagram = 1 if websites.get('instagram', 'failed') == 'success' else 0
                server.is_access_facebook = 1 if websites.get('facebook', 'failed') == 'success' else 0
                server.is_access_telegram = 1 if websites.get('telegram', 'failed') == 'success' else 0
            else:
                logger.error(f"[ReceiveServerReport] extra_info not found in report, ip: {ip}, client info: {i}")

            # 检查 test_result
            if str(i['test_result']).lower() == "false":
                current_failed_cnt = server.fail_count
                server.fail_count += 1
                server.available = 0
                if server.fail_count >= 3:
                    is_alert = True
                    need_alert_list.append(i)
                    logger.warning(
                        f"[ReceiveServerReport] server disabled after 3 fails, ip: {ip}, client info: {i}")
                else:
                    logger.warning(
                        f"[ReceiveServerReport] server disabled, ip: {ip}, fail_count:{current_failed_cnt} client info: {i}")
            else:
                normal_cnt += 1
                if server.available == 0:
                    logger.info(f"[ReceiveServerReport] server recovered, ip: {ip}, client info: {i}")
                server.available = 1
                server.fail_count = 0  # 恢复时清零

            # 特殊的：如果全部的主流网站都挂了，那也算是不可用
            if server.is_access_youtube == 0 and server.is_access_tiktok == 0 and server.is_access_instagram == 0 and server.is_access_facebook == 0 and server.is_access_telegram == 0:
                server.available = 0

            server.save()

        # 只有发现异常节点，才告警
        if is_alert:
            ans_print_str = "\n"
            for i in need_alert_list:
                ans_print_str += f"{i['ip']} | {i['test_result']} | {i['extra_info']['hostname']} | {i['extra_info']['country']}"
                ans_print_str += "\n"
            ans_print_str += f"normal cnt: {normal_cnt}"
            logger.warning(f"[ReceiveServerReport] {ans_print_str}")
        return self.ReturnSuccess()


class ReceiveUserUsageReport(CommonView):
    """
        {
      "date": "2025-08-18",
      "traffic":[
        {"id": "fd6080a4-ff13-494e-93b3-fa8fce09fff1","total": 80}
      ]
    }
    """

    def post(self, request):
        try:
            post_data, err_code, err_msg = self.ReadPostJson(request, check_list=('token',))
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            token = post_data["token"]
            if token != "!@cdioshcs12!@DA#!s\"":
                return self.ReturnError("token error")

            if "traffic" not in post_data:
                logger.error(f"[ReceiveUserUsageReport] traffic: {post_data['traffic']}")
                return self.ReturnError("need traffic param")

            for i in post_data["traffic"]:
                ray_id = i["id"]
                # 这是默认的测试rayid，不需要上报
                if ray_id == "BF58DDB2-F0CE-479B-BA0E-C77AB65087CD":
                    continue

                usage_cnt = i["total"]
                user = User.objects.filter(ray_id=ray_id).first()
                if not user:
                    logger.warning(f"[ReceiveUserUsageReport] user not found from ray_id:{ray_id}")
                    continue

                device_id = user.device_id
                user_traffic = UserDao.get_user_traffic(device_id)
                if user_traffic:
                    user_traffic.usage_cnt += usage_cnt
                    user_traffic.save()
                else:
                    try:
                        UserTraffic(device_id=device_id, ray_id=ray_id, usage_cnt=usage_cnt).save()
                    except Exception as e:
                        if "Duplicate entry" in str(e):
                            logger.warning(
                                f"[ReceiveUserUsageReport] user_traffic already exists, device_id:{device_id}")
                        else:
                            logger.error(f"[ReceiveUserUsageReport] save user_traffic failed, device_id:{device_id}",
                                         exc_info=True)

            return self.ReturnSuccess()
        except Exception:
            logger.error("ReceiveUserUsageReport failed", exc_info=True)
            return self.ReturnError("ReceiveUserUsageReport failed")


class ChangeClientsIpNotify(CommonView):
    @staticmethod
    def notify_all_pay_users():
        try:
            all_pay_users = Order.objects.filter(order_status='OPEN', valid=1).all()
            all_size = len(all_pay_users)
            logger.info(f"[ChangeClientsIpNotify] all_pay_users size: {all_size}")

            push_content = """Приветствуем вас! Мы обновили список серверов. Пожалуйста, подключитесь к нашему приложению, чтобы получить более стабильную и быструю скорость.
    
Greeting, we have updated our server list, please reconnect with our APP to get more stable and faster speed."""

            # 找到最近6天有使用的用户
            need_notify_push_ids = []
            for index, i in enumerate(all_pay_users):
                if index % 1000 == 0:
                    logger.info(f"[ChangeClientsIpNotify] all_pay_users progress: {index}/{all_size}")

                device_id = i.device_id
                usage = UserUsage.objects.filter(device_id=device_id).first()
                if usage and abs(TimeUtil.GetDiffDays(TimeUtil.GetNow(), usage.updated_at)) <= 6:
                    # 写到通知中心
                    number = SmsTool.get_mock_number_by_device_id(device_id)
                    SmsItSupportTool.add_support_sms_without_push(device_id, settings.SMS_DIRECTION_RECEIVE,
                                                                  settings.APP_IT_SUPPORT_SHOW_PHONE, number,
                                                                  push_content)

                    # 拿到推送id
                    user = User.objects.filter(device_id=device_id).first()
                    if user:
                        if user.jpush_id:
                            need_notify_push_ids.append(user.jpush_id)
                        else:
                            logger.warning(
                                f"[ChangeClientsIpNotify] user jpush_id not found from device_id:{device_id}, usage.updated_at: {usage.updated_at}, now:{TimeUtil.GetNow()}, diff:{TimeUtil.GetDiffDays(usage.updated_at, TimeUtil.GetNow())}")
                    else:
                        logger.error(f"[ChangeClientsIpNotify] user not found from device_id:{device_id}")

            logger.info(
                f"[ChangeClientsIpNotify] need_notify_push_ids active in 6 days size: {len(need_notify_push_ids)}")

            PushUtil.batch_notify_by_fcm(need_notify_push_ids, push_content)
            logger.info(f"[ChangeClientsIpNotify] all_pay_users size: {all_size} push done")
        except Exception:
            logger.error(f"[ChangeClientsIpNotify] failed", exc_info=True)

    def post(self, request):
        try:
            post_data, err_code, err_msg = self.ReadPostJson(request, check_list=('token',))
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            token = post_data["token"]
            if token != "dajkDWDn1kejipndsk!@#sDEDDCME":
                return self.ReturnError("token error")

            t = threading.Thread(target=self.notify_all_pay_users, args=())
            t.start()

            logger.info(f"[ChangeClientsIpNotify] all_pay_users create thread done")
            return self.ReturnSuccess()
        except Exception:
            logger.error("ChangeClientsIpNotify failed", exc_info=True)
            return self.ReturnError("ChangeClientsIpNotify failed")


class ChangeClientsIpNotifyTest(CommonView):
    def post(self, request):
        try:
            post_data, err_code, err_msg = self.ReadPostJson(request, check_list=('token',))
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            token = post_data["token"]
            if token != "dajkDWDn1kejipndsk!@#sDEDDCME":
                return self.ReturnError("token error")

            push_content = """Приветствуем вас! Мы обновили список серверов. Пожалуйста, подключитесь к нашему приложению, чтобы получить более стабильную и быструю скорость.

Greeting, we have updated our server list, please reconnect with our APP to get more stable and faster speed."""

            PushUtil.batch_notify_by_fcm_test(push_content, 100)

            # 写到通知中心
            device_id = "df5a230c-cfe9-4842-ad75-80b501851b89"
            number = SmsTool.get_mock_number_by_device_id(device_id)
            SmsItSupportTool.add_support_sms_without_push(device_id, settings.SMS_DIRECTION_RECEIVE,
                                                          settings.APP_IT_SUPPORT_SHOW_PHONE, number,
                                                          push_content)

            logger.info(f"[ChangeClientsIpNotify] all_pay_users create thread done")
            return self.ReturnSuccess()

        except Exception:
            logger.error("ChangeClientsIpNotify failed", exc_info=True)
            return self.ReturnError("ChangeClientsIpNotify failed")
