'''
Created on Oct 6, 2021

@author: jon
'''

import json

from django.contrib import admin
from django.db import models


class Config(models.Model):
    config = models.TextField('config')
    channel = models.CharField('channel', blank=True, max_length=50)
    version = models.IntegerField('version', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


@admin.register(Config)
class ConfigAdmin(admin.ModelAdmin):

    def check_json(self, object):
        try:
            json.loads(object.config)
            return object.config
        except Exception as e:
            return 'json error'

    files = ('config', 'channel', 'version')
    list_display = ('id', 'check_json', 'channel', 'version', 'updated_at')
    list_display_link = ('id',)
    list_per_page = 50
