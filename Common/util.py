"""
Created on 2021/1/24

@author: jon
"""
import hashlib
import random
import re
import string

from django.core.cache import cache


class Util:

    @staticmethod
    def format_number(number: str) -> str:
        tmp_number = "+"
        for i in number:
            if i.isdigit():
                tmp_number += i
        return tmp_number

    @staticmethod
    def has_alphanumeric_chars(text: str):
        pattern = r'[a-zA-Z]'
        match = re.search(pattern, text)
        return match is not None

    @staticmethod
    def mask_emails_correctly(text: str) -> str:
        def mask_email(match):
            email = match.group(0)  # 匹配到的完整邮箱
            username, domain = email.split("@", 1)  # 分割用户名和域名
            masked_username = username[0] + "*" * (len(username) - 1)  # 保留首字母
            return f"{masked_username}@{domain}"  # 组合回邮箱格式

        # 匹配邮箱的正则表达式
        email_regex = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
        return re.sub(email_regex, mask_email, text)

    @staticmethod
    def FormatInt(number):

        try:
            return int(number)
        except Exception as e:
            return 0

    @staticmethod
    def MD5Sum(s):

        m = hashlib.md5()
        m.update(s.encode('utf-8'))
        return m.hexdigest()

    @staticmethod
    def Write2Cache(key, val, timeout=3600):
        cache.set(key, val, timeout)

    @staticmethod
    def GetFromCache(key):
        return cache.get(key)

    @staticmethod
    def DeleteCache(key):
        cache.delete(key)

    @staticmethod
    def GenRandomStr(length: int):

        random_str_list = []
        for _ in range(length):
            random_str_list.append(''.join(random.sample(string.ascii_letters, 1)))
        return ''.join(random_str_list)

    @staticmethod
    def GenRandomDigit(length: int):

        random_str_list = []
        for _ in range(length):
            random_str_list.append(''.join(random.sample(string.digits, 1)))
        return ''.join(random_str_list)
