"""
Created on 2021/10/16

@author: jon
"""


### 1：error level
#   1: sign error, 2: auth error, 3: parameter error,
#   4:business error  5: service error, 6: unknown error

### 2-3: error module or function
#   00: common module, 01: sign check, 02: token check, 03: signup,
#   04: signin, 05: signout, 06: reset password, 07: bind account

### 4-5: detail code


class ErrInfo:
    SUCCESS = 0

    SIGN_PARAMETER_MISS = 10100
    SIGN_TIMESTAMP_EXPIRED = 10101
    SIGN_INCORRECT = 10102

    AUTH_PARAMETER_MISS = 20200
    AUTH_TOKEN_INVALID = 20201
    AUTH_DUPLICATE_REQUEST = 20202

    JSON_FORMAT_ERROR = 30000
    JSON_REQUEST_PARA_MISS = 30001

    SIGNUP_PARAMETER_MISS = 30300
    SIGNUP_SERVER_ERROR = 30301
    SIGNIN_PARAMETER_MISS = 30400

    SIGNUP_EMAIL_EXISTS = 40301

    SIGNIN_PASSWORD_INCORRECT = 40400
    SIGNIN_EXCEED_MAX_DEVICE_NUM = 40401

    SIGNOUT_DEVICE_NOT_FOUND = 40501

    RESET_PASSWORD_EMAIL_NOT_EXIST = 40601
    RESET_PASSWORD_EMAIL_WRONG_VERIFY_CODE = 40602

    DEVICE_BIND_MORE_THAN_ONE_ACCOUNT = 40701
    NO_SERVER_TO_USE = 40702

    # order
    INVALID_CERT = 40800
    DUPLICATE_CERT = 40801

    SERVER_ERROR = 60000
    TO_MANY_AD_ERROR = 60001
    UNKNOWN_SERVER_ERROR = 60002
    ORDER_BELONGS_TO_OTHER_ACCOUNT = 60014

    # app遇到7000x会跳转
    JUMP_VIEW = 70000
    JUMP_VIP_VIEW = 70001

    ErrMsg_EN = {
        0: "success",

        10100: "Missing signature parameter. Please verify your request.",
        10101: "Signature timestamp has expired. Please retry with a fresh timestamp.",
        10102: "Invalid signature. Please verify and try again.",

        20200: "Missing authentication parameter. Please provide all required credentials.",
        20201: "Authentication token is invalid or expired. Please log in again.",
        20202: "Duplicate request detected. Please wait before retrying.",

        30000: "Invalid JSON format. Please check your request format.",
        30001: "Missing required JSON parameter. Please check the API documentation.",

        30300: "Missing registration parameters. Please provide all required information.",
        30301: "Server error during registration. Please try again in a few moments.",
        30400: "Missing login parameters. Please provide all required credentials.",

        40301: "Email address is already registered. Please use a different email.",

        40400: "Incorrect username or password. Please verify your credentials.",
        40401: "Maximum device limit reached. Please remove an existing device to continue.",

        40501: "Device not found or already unbound from your account.",

        40601: "Email address not found in our system. Please check the address.",
        40602: "Invalid or expired verification code. Please request a new code.",

        40701: "Device is linked to multiple accounts. Please unbind from other accounts first.",
        40702: "No servers available at the moment. Please contact customer service support for assistance.",

        40800: "Invalid certificate or password. Please verify and try again.",
        40801: "Subscription is active on another device. Please sign in on that device or contact support.",

        60000: "Server error encountered. Please try again later.",
        60001: "Daily advertisement limit reached. Please try again tomorrow.",
        60002: "Server error encountered. Please try again later.",
        60014: "Your subscription has been bound to another device. Please contact the customer service center to unbind it for you.",

        70000: "Redirecting to the requested page...",
        70001: "Redirecting to VIP subscription page..."
    }

    ErrMsg_RU = {
        0: "success",
        10100: "Отсутствует параметр подписи. Пожалуйста, проверьте запрос.",
        10101: "Срок действия подписи истек. Пожалуйста, повторите попытку.",
        10102: "Неверная подпись. Пожалуйста, проверьте и попробуйте снова.",

        20200: "Отсутствует параметр аутентификации. Предоставьте все необходимые данные.",
        20201: "Недействительный или просроченный токен. Пожалуйста, войдите снова.",
        20202: "Обнаружен повторный запрос. Пожалуйста, подождите перед повторной попыткой.",

        30000: "Неверный формат JSON. Проверьте формат запроса.",
        30001: "Отсутствует обязательный параметр JSON. Проверьте документацию API.",

        30300: "Отсутствуют параметры регистрации. Укажите всю необходимую информацию.",
        30301: "Ошибка сервера при регистрации. Повторите попытку через несколько минут.",
        30400: "Отсутствуют параметры входа. Укажите все необходимые данные.",

        40301: "Этот email уже зарегистрирован. Используйте другой адрес.",

        40400: "Неверное имя пользователя или пароль. Проверьте учетные данные.",
        40401: "Достигнут лимит устройств. Удалите существующее устройство для продолжения.",

        40501: "Устройство не найдено или уже отвязано от вашей учетной записи.",

        40601: "Email не найден в системе. Проверьте адрес.",
        40602: "Неверный или просроченный код подтверждения. Запросите новый код.",

        40701: "Устройство привязано к нескольким аккаунтам. Сначала отвяжите другие аккаунты.",
        40702: "Нет доступных серверов. Обратитесь в службу поддержки.",

        40800: "Недействительный сертификат или пароль. Проверьте и попробуйте снова.",
        40801: "Подписка активна на другом устройстве. Войдите на том устройстве или обратитесь в поддержку.",
        60000: "Произошла ошибка сервера. Повторите попытку позже.",
        60001: "Достигнут дневной лимит рекламы. Попробуйте завтра.",
        60014: "Ваша подписка уже привязана к другому устройству. Пожалуйста, свяжитесь с центром поддержки, чтобы его отвязали.",

        70000: "Перенаправление на запрошенную страницу...",
        70001: "Перенаправление на страницу VIP-подписки..."
    }
