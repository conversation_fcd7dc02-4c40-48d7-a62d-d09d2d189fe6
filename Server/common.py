import math
import random
from collections import defaultdict
from typing import Union

from django.conf import settings
from django.core.cache import cache

from Common.rediskey import RedisKey
from Common.views import CommonView
from Lamp.settings import logger
from Server.models import Server
from Server.remote_control import RemoteControlServer
from Server.dao.server_dao import ServerDao
from User.models import UserUsage, User


class ServerCommon(CommonView):
    # 缓存过期时间：10分钟
    RECENT_IP_CACHE_TIMEOUT = 600

    @staticmethod
    def get_server_with_less_user_with_ray(device_id: str, country: str) -> Union[Server, None]:

        country = country.upper()

        # 查询某个国家的服务器
        servers = ServerDao.get_servers_by_country(country)
        logger.info(
            f"[GetServerWithLessUserWithRay] device_id:{device_id}, country:{country}, count: {servers.count()}")

        # 降级用全部的服务器
        if servers.count() == 0:
            logger.info(f"[GetServerWithLessUserWithRay] device_id:{device_id}, country:{country} 0, try random")
            servers = ServerDao.get_all_valid_servers()
        if servers.count() == 0:
            logger.error(f"[GetServerWithLessUserWithRay] device_id:{device_id}, all ray server count is 0")
            return None

        # 放弃倒霉蛋
        server_count = len(servers)
        if server_count == 1:
            selected_server = servers[0]
        elif server_count <= 5:
            # 排除最后一台服务器
            selected_server = random.choice(servers[0:server_count - 1])
        else:
            # 排除最后的1/4
            cnt = math.floor(server_count / 4)
            selected_server = random.choice(servers[0:server_count - cnt])

        logger.info(f"[GetServerWithLessUserWithRay] device_id:{device_id}, country:{country} "
                    f"ip:{selected_server.ip}, country:{selected_server.country}, city: {selected_server.city}, "
                    f"version: {selected_server.version}, online user:{selected_server.online_user}")
        return selected_server

    @staticmethod
    def get_server_with_less_user_with_ray_without_ips(device_id: str, country: str, ips: list) -> Union[Server, None]:

        country = country.upper()

        # 查询某个国家的服务器
        servers = ServerDao.get_servers_by_country_exclude_ips(country, ips)
        logger.info(
            f"[GetServerWithLessUserWithRayWithOutIps] device_id:{device_id}, country:{country}, count: {servers.count()}")

        # 降级用全部的服务器
        if servers.count() == 0:
            logger.info(
                f"[GetServerWithLessUserWithRayWithOutIps] device_id:{device_id}, country:{country} count is 0, try a random country")
            servers = ServerDao.get_all_servers_exclude_ips(ips)

        # 依旧没有服务器计算了
        if servers.count() == 0:
            logger.error(f"[GetServerWithLessUserWithRayWithOutIps] device_id:{device_id}, all ray server count is 0")
            return None

        # 放弃倒霉蛋
        server_count = len(servers)
        if server_count == 1:
            selected_server = servers[0]
        elif server_count <= 5:
            # 排除最后一台服务器
            selected_server = random.choice(servers[0:server_count - 1])
        else:
            # 排除最后的1/4
            cnt = math.floor(server_count / 4)
            selected_server = random.choice(servers[0:server_count - cnt])

        logger.info(f"[GetServerWithLessUserWithRayWithOutIps] device_id:{device_id}, country:{country} "
                    f"ip:{selected_server.ip}, country:{selected_server.country}, city: {selected_server.city}, "
                    f"version: {selected_server.version}, online user:{selected_server.online_user}")
        return selected_server

    @staticmethod
    def __get_average_user_by_country(country):

        servers = ServerDao.get_servers_by_country(country)
        server_number = servers.count()
        if server_number == 0:
            logger.error(f"__get_average_user_by_country country: {country} without available servers")
            return 'NO-AVAILABLE'

        total_user = 0
        for s in servers:
            total_user += s.online_user

        average_user = int(total_user / server_number)

        if average_user < settings.IDLE:
            return 'IDLE'

        if average_user > settings.BUSY:
            return 'BUSY'

        return 'MEDIUM'

    def compose_country_info_limit10(self, lang):

        res = []
        servers = ServerDao.get_all_valid_servers()
        server_cnt_map = defaultdict(int)
        for s in servers:
            # 一个国家最多展示10个节点
            if server_cnt_map[s.country] >= 10:
                continue

            server_cnt_map[s.country] += 1

            server_info = self.compose_country_info(lang, s, server_cnt_map[s.country])

            res.append(server_info)

        # add fake countries
        res = self.add_fake_countries(res, lang)

        # sort
        res = sorted(res, key=self.custom_sort_country)

        return res

    @staticmethod
    def compose_country_info(lang, s, country_servers_cnt: int):
        name, ms, mbs, title = ServerCommon.wrap_fake_country_name_ms_mbs(s.country, country_servers_cnt, lang)
        status = ServerCommon.__get_average_user_by_country(s.country)
        server_info = {
            'country': s.country,
            'name_en': name,
            'flag': f"https://cdn.gongbaojiding.xyz/flag/{s.country}.png",
            # 'flag': settings.FLAG_ADDRESS + s.country + '.png',
            'status': status,
            "speed_ms": f"{ms} ms",  # 延迟：20 ms
            "speed_mbs": f"{mbs} Mb/s",  # 贷宽：20 Mb/s
            "title": title,
        }
        return server_info

    @staticmethod
    def compose_country_info_v2(lang, s, country_servers_cnt: int):
        name, ms, mbs, title = ServerCommon.wrap_fake_country_name_ms_mbs(s.country, country_servers_cnt, lang)
        status = 'IDLE'
        server_info = {
            'country': s.country,
            'name_en': name,
            'flag': f"https://cdn.gongbaojiding.xyz/flag/{s.country}.png",
            # 'flag': settings.FLAG_ADDRESS + s.country + '.png',
            'status': status,
            "speed_ms": f"{ms} ms",  # 延迟：20 ms
            "speed_mbs": f"{mbs} Mb/s",  # 贷宽：20 Mb/s
            "title": title,
        }
        return server_info

    # 自定义排序函数
    @staticmethod
    def custom_sort_country(item):
        # 将 'United States' 和 'Singapore' 排在前面
        if item['country'] == 'US':
            return 0, item['name_en']
        elif item['country'] == 'SGP':
            return 1, item['name_en']
        else:
            return 2, item['name_en']

    @staticmethod
    def wrap_fake_country_name_ms_mbs(country: str, current_node_cnt: int, lang: str) -> (str, str, str, str):
        mbs_values = ["15", "20", "30"]
        # 节点越靠前，延迟越小
        ms = random.randint(15 * (current_node_cnt + 1), 35 * (current_node_cnt + 1))
        mbs = random.choice(mbs_values)
        if lang == "ru":
            name = settings.COUNTRY_RU.get(country)
        else:
            name = settings.COUNTRY_EN.get(country)
        title = name + f"({current_node_cnt})"
        fake_name = name + f"({current_node_cnt}) - {ms}ms-{mbs}Mb/s"
        return fake_name, ms, mbs, title

    @staticmethod
    def add_fake_countries(original_list: list, lang: str) -> list:
        # 计算每个国家的节点数量
        country_node_count = defaultdict(int)
        for item in original_list:
            country = item['country']
            country_node_count[country] = country_node_count[country] + 1

        # 处理过的国家标志
        add_map = defaultdict(bool)

        # 新列表
        fake_list = []

        for item in original_list:
            country = item['country']

            # 一个国家只能加一次
            if add_map[country]:
                continue
            add_map[country] = True

            flag = item['flag']
            status = item['status']
            node_count = country_node_count[country]

            # 如果这个国家已经有很多节点了，请放弃add fake
            if node_count >= 3:
                continue

            # 生成新节点信息并添加到新列表中
            for i in range(0, 2):
                fake_name, ms, mbs, title = ServerCommon.wrap_fake_country_name_ms_mbs(country, node_count + i + 1,
                                                                                       lang)
                fake_item = {
                    'country': country,
                    'flag': flag,
                    'status': status,
                    "name_en": fake_name,
                    "speed_ms": f"{ms} ms",  # 延迟：20 ms
                    "speed_mbs": f"{mbs} Mb/s",  # 贷宽：20 Mb/s
                    "title": title,
                }
                fake_list.append(fake_item)

        # 合并原始列表和fake列表
        result_list = original_list + fake_list

        return result_list

    @staticmethod
    def get_server_by_country_with_ray(country: str, device_id: str) -> dict:

        # 获取最少使用的节点ip
        less_user_server = ServerCommon.get_server_with_less_user_with_ray(device_id, country)
        if not less_user_server:
            logger.error(f"[GetServerByCountryWithRay] device_id:{device_id}, country:{country}, no server available")
            return {}

        info = {
            "server_id": str(less_user_server.id),
            "ip": less_user_server.ip,
            "country": less_user_server.country,
        }
        logger.info(f"[GetServerByCountryWithRay] device_id:{device_id}, final server:{info}")
        return info

    @staticmethod
    def get_server_by_country_with_ray_without_ips(country: str, device_id: str, ips: list) -> dict:

        # 获取最少使用的节点ip
        less_user_server = ServerCommon.get_server_with_less_user_with_ray_without_ips(device_id, country, ips)
        if not less_user_server:
            logger.error(
                f"[GetServerByCountryWithRayWithoutIps] device_id:{device_id}, country:{country}, no server available")
            return {}

        info = {
            "server_id": str(less_user_server.id),
            "ip": less_user_server.ip,
            "country": less_user_server.country,
        }
        logger.info(f"[GetServerByCountryWithRayWithoutIps] device_id:{device_id}, final server:{info}")
        return info

    @staticmethod
    def clear_user_recent_ip_cache(device_id: str, country: str):
        """
        清理用户的IP缓存记录，用于主动切换时重置计时
        """
        cache_key = RedisKey.gen_recent_ip_key(device_id, country)
        cache.delete(cache_key)
        logger.info(f"[clear_user_recent_ip_cache] device_id:{device_id}, country:{country}, cleared recent ip cache")

    @staticmethod
    def delete_client_user(device_id: str):
        try:
            user = User.objects.filter(device_id=device_id).first()
            if not user:
                logger.error(f"[DeleteClientUser] device not exist: {device_id}")
                return

            # 删除之前下发的账户
            UserUsage.objects.filter(device_id=device_id).delete()

        except Exception:
            logger.error(f"[DeleteClientUser] failed, device_id:{device_id}", exc_info=True)

    @staticmethod
    def batch_delete_client_user(device_id_list: list):
        try:
            ray_users = []

            for device_id in device_id_list:
                user = User.objects.filter(device_id=device_id).first()
                if user and user.ray_id:
                    ray_users.append(device_id)

            logger.info(f"[BatchDeleteClientUser] ray_users:{ray_users}")

            # 所有服务器都通知下线
            all_servers = Server.objects.all()
            for s in all_servers:
                if len(ray_users) > 0:
                    del_err = RemoteControlServer().BatchDelUsersWithRay(s.ip, ray_users)
                    if len(del_err) > 0:
                        logger.warning(f"[BatchDeleteClientUser] remote control error, del ray server, "
                                       f"device_id: {ray_users}, failed: {del_err}")

            # 删除之前下发的账户
            for device_id in device_id_list:
                UserUsage.objects.filter(device_id=device_id).delete()

        except Exception:
            logger.error(f"[BatchDeleteClientUser] failed, device_id_list:{device_id_list}", exc_info=True)
