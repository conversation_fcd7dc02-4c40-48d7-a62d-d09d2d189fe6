import json
import uuid

from Common.err import ErrInfo
from Common.views import CommonView
from Lamp.settings import logger
from Order.common import Order<PERSON>ommon
from Server.common import ServerCommon
from Server.models import Server
from Server.remote_control import RemoteControlServer
from Server.util import ServerUtil
from User.models import <PERSON>r<PERSON><PERSON><PERSON>, UsageRecord, User


class GetServerList(CommonView):

    def post(self, request):
        try:
            post_data, err_code, err_msg = self.ReadPostJson(request, check_list=['token', ])
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            token = post_data['token']
            if token != "XiYang$YangMEiYY$":
                logger.error(f"[GetServerList] invalid token: {token}")
                return self.ReturnError(ErrInfo.SERVER_ERROR)

            servers = Server.objects.all()
            rsp = []
            for server in servers:
                rsp.append({
                    "id": server.id,
                    "ip": server.ip,
                    "hostname": server.hostname,
                    "country": server.country,
                    "version": server.version,
                    "available": server.available,
                    "created_at": server.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                })

            return self.ReturnSuccess({
                "servers": rsp
            })

        except Exception:
            logger.error("GetServerList failed", exc_info=True)
            return self.ReturnError(ErrInfo.SERVER_ERROR)


class GetServerCountryList(CommonView):

    # post /server/country/
    @CommonView.VerifySign
    def post(self, request):
        try:
            headers = self.GetHeaderInRequest(request)
            lang = headers["lang"]
            common = ServerCommon()
            info = {
                "servers": common.compose_country_info_limit10(lang)
            }

            headers = self.GetHeaderInRequest(request)
            logger.info(
                f'GetServerCountryList device_id:{headers["device_id"]}, country info size: {len(info["servers"])}')

            return self.ReturnSuccess(info)

        except Exception:
            logger.error("GetServerCountryList failed", exc_info=True)
            return self.ReturnError(ErrInfo.SERVER_ERROR)


class GetServerProfile(CommonView):

    # get /server/profile/
    @CommonView.VerifySign
    def post(self, request):
        headers = self.GetHeaderInRequest(request)
        logger.warning(f"[GetServerProfile] please update new app version: {request.path}, {headers}")
        return self.ReturnError(ErrInfo.SERVER_ERROR, "Please update to the new app version.")


class GetRayServerProfile(CommonView):

    @CommonView.VerifySign
    def post(self, request):
        try:
            data, err_code, err_msg = self.ReadPostJson(request, ('country',))
            if err_code != 0:
                logger.error(f"[GetRayServerProfile] param error: {request.body}")
                return self.ReturnError(err_code, err_msg)

            headers = self.GetHeaderInRequest(request)
            country = data['country']
            req_country = country
            device_id = headers['device_id']
            logger.info(f"[GetRayServerProfile] device_id:{device_id}, country: {country}")
            is_expired, expired_at = OrderCommon.is_expired(device_id)
            if is_expired:
                logger.warning(
                    f"[GetRayServerProfile] device_id:{device_id}, country: {country}, user is not vip, return")
                return self.ReturnError(ErrInfo.JUMP_VIP_VIEW)

            # 拿到 ray_id
            user = User.objects.filter(device_id=device_id).first()
            if not user.ray_id:
                user.ray_id = str(uuid.uuid4())
                user.save()

            # info: {ip, hostname, country}
            info = ServerCommon.get_server_by_country_with_ray(country, device_id)
            country = info['country']  # 可能你请求US，但因为资源问题改为UK，这里要同步修改
            ip = info['ip']
            logger.info(f"[GetRayServerProfile] device_id:{device_id}, country: {country} user get server:{info}")

            # 通知client下发
            ray_id = user.ray_id
            err = RemoteControlServer().AddUserWithRay(ip, ray_id)
            if err:
                logger.warning(f"[GetRayServerProfile] user assign to ray server failed, device:{device_id}, err:{err}")
                info = ServerCommon.get_server_by_country_with_ray(country, device_id)
                ip = info['ip']
                err = RemoteControlServer().AddUserWithRay(ip, ray_id)
                if err:
                    logger.error(
                        f"[GetRayServerProfile] user double assign to ray server failed, device:{device_id}, err:{err}")
                    return self.ReturnError(ErrInfo.NO_SERVER_TO_USE)
            else:
                logger.info(
                    f"[GetRayServerProfile] user assign to ray server success, device:{device_id}, server info:{info}")

            # 存储
            usage = self.GetOrNone(UserUsage, device_id=device_id)
            if usage is None:
                usage = UserUsage(device_id=device_id, account_id='', ip=info['ip'], country=info['country'],
                                  password='', ray_id=user.ray_id)
                usage.save()
            else:
                usage.ray_id = user.ray_id
                usage.country = info['country']
                usage.password = ''
                usage.account_id = ''
                usage.ip = info['ip']
                usage.save()

            # 记录下每次的下发
            logger.info(f"[GetRayServerProfile] user success assign to a server, add record device:{device_id}")
            record = UsageRecord(device_id=device_id, account_id="", ip=ip, country=info['country'],
                                 password="", ray_id=ray_id, req_country=req_country)
            record.save()

            # rsp
            rsp = {
                'server': ip,
                'port': 443,
                'type': 'vmess',
                'uuid': ray_id,
                'alterId': 0,
                'cipher': 'auto',
                'tls': True,
                'skip-cert-verify': True,
                'network': 'ws',
                'path': '/user',
                'host': 'api.gongbaojiding.xyz',
                'udp': True
            }
            encrypt_info = ServerUtil.AESEncode(json.dumps(rsp))
            logger.info(f'[GetRayServerProfile] server: {rsp}, encrypt_info:{encrypt_info}')

            return self.ReturnSuccess(encrypt_info)
        except Exception:
            logger.error("[GetRayServerProfile] failed", exc_info=True)
            return self.ReturnError(ErrInfo.SERVER_ERROR)
