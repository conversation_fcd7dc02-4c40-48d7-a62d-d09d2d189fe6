# Generated by Django 3.2.5 on 2022-10-08 02:39

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='OnlineUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('non_vip', models.IntegerField(verbose_name='non vip user')),
                ('vip', models.IntegerField(verbose_name='vip user')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Server',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip', models.CharField(max_length=50, verbose_name='IP')),
                ('hostname', models.CharField(default='HOSTNAME', max_length=50, verbose_name='Hostname')),
                ('online_user', models.IntegerField(default=0, verbose_name='Online User')),
                ('country', models.CharField(blank=True, max_length=50, verbose_name='Country')),
                ('enabled', models.IntegerField(default=0)),
                ('is_vip', models.IntegerField(default=0)),
                ('version', models.CharField(default='v1', max_length=10, verbose_name='version')),
                ('remote_error', models.CharField(default='', max_length=50, verbose_name='remote_error')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
