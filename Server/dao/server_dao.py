from django.core.cache import cache

from Lamp.settings import logger
from Server.models import Server


class ServerDao:
    # 缓存键名，用于标识此函数的缓存结果
    _UNAVAILABLE_THRESHOLD_CACHE_KEY = "server_unavailable_threshold_hit"
    # 缓存过期时间：5分钟（300秒），与状态更新频率保持一致
    _CACHE_EXPIRE_SECONDS = 300

    @staticmethod
    def is_hit_un_available_threshold() -> bool:
        # 尝试从缓存获取结果
        cached_result = cache.get(ServerDao._UNAVAILABLE_THRESHOLD_CACHE_KEY)

        # 如果缓存存在，直接返回缓存结果
        if cached_result is not None:
            # logger.info(f"[is_hit_un_available_threshold] hit cached_result: {cached_result}")
            return cached_result

        # 缓存不存在时，执行原始计算逻辑
        servers = Server.objects.all()
        available_cnt = 0
        un_available_cnt = 0

        for server in servers:
            if server.available == 1:
                available_cnt += 1
            else:
                un_available_cnt += 1

        # 避免除以零错误（当没有服务器时）
        total = un_available_cnt + available_cnt
        if total == 0:
            logger.error("[is_hit_un_available_threshold] no server")
            # 没有服务器时默认返回False，同时缓存结果
            cache.set(
                ServerDao._UNAVAILABLE_THRESHOLD_CACHE_KEY,
                False,
                ServerDao._CACHE_EXPIRE_SECONDS
            )
            return False

        rate = un_available_cnt / total

        # 超过0.3就报警，但是不做操作
        if rate >= 0.3:
            logger.error(f"[is_hit_un_available_threshold] unavailable hit rate: {rate}")

        # 超过0.7就不再考虑 available字段了
        if rate >= 0.7:
            cache.set(
                ServerDao._UNAVAILABLE_THRESHOLD_CACHE_KEY,
                True,
                ServerDao._CACHE_EXPIRE_SECONDS
            )
            return True

        # 其他情况正常，将计算结果存入缓存
        cache.set(
            ServerDao._UNAVAILABLE_THRESHOLD_CACHE_KEY,
            False,
            ServerDao._CACHE_EXPIRE_SECONDS
        )
        return False

    @staticmethod
    def get_servers_by_country(country: str):
        if ServerDao.is_hit_un_available_threshold():
            servers = Server.objects.filter(enabled=1, country=country).order_by('online_user')
        else:
            servers = Server.objects.filter(enabled=1, available=1, country=country).order_by('online_user')
        return servers

    @staticmethod
    def get_all_valid_servers():
        if ServerDao.is_hit_un_available_threshold():
            servers = Server.objects.filter(enabled=1).order_by('online_user')
        else:
            servers = Server.objects.filter(enabled=1, available=1).order_by('online_user')
        return servers

    @staticmethod
    def get_all_enabled_servers():
        servers = Server.objects.filter(enabled=1)
        return servers

    @staticmethod
    def get_all_enabled_servers_cnt() -> int:
        return Server.objects.filter(enabled=1).count()

    @staticmethod
    def get_all_enabled_available_servers_cnt() -> int:
        return Server.objects.filter(enabled=1, available=1).count()

    @staticmethod
    def get_all_enabled_unavailable_servers_cnt() -> int:
        return Server.objects.filter(enabled=1, available=0).count()

    @staticmethod
    def get_servers_by_country_exclude_ips(country: str, ips: list):
        if ServerDao.is_hit_un_available_threshold():
            servers = (Server.objects.filter(enabled=1, country=country)
                       .exclude(ip__in=ips)
                       .order_by('online_user'))
        else:
            servers = (Server.objects.filter(enabled=1, available=1, country=country)
                       .exclude(ip__in=ips)
                       .order_by('online_user'))

        return servers

    @staticmethod
    def get_all_servers_exclude_ips(ips: list):
        if ServerDao.is_hit_un_available_threshold():
            servers = (Server.objects.filter(enabled=1)
                       .exclude(ip__in=ips)
                       .order_by('online_user'))
        else:
            servers = (Server.objects.filter(enabled=1, available=1)
                       .exclude(ip__in=ips)
                       .order_by('online_user'))

        return servers

    @staticmethod
    def get_valid_server_by_ip(ip: str):
        if ServerDao.is_hit_un_available_threshold():
            return Server.objects.filter(ip=ip, enabled=1).first()

        return Server.objects.filter(ip=ip, enabled=1, available=1).first()

    @staticmethod
    def get_server_by_ip_without_condition(ip: str):
        return Server.objects.filter(ip=ip).first()

    @staticmethod
    def get_server_by_hostname(hostname: str):
        return Server.objects.filter(hostname=hostname)

    @staticmethod
    def update_err_by_ip(ip: str, err_msg: str):
        Server.objects.filter(ip=ip).update(remote_error=err_msg[0:512])
