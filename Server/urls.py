from django.conf.urls import url

from Server import views, report, online
from Server import views_client
from Server import views_old

urlpatterns = [
    # for app [待删除]
    url(r'^lpapi/server/country/$', views_old.GetServerCountryList.as_view()),
    url(r'^lpapi/server/list/$', views_old.GetServerList.as_view()),  # admin test node
    url(r'^lpapi/server/profile/$', views_old.GetServerProfile.as_view()),  # 废弃
    url(r'^lpapi/server/profile/v2/$', views_old.GetRayServerProfile.as_view()),

    # for new app
    url(r'^lpapi/server/profile/node_list/$', views.GetRayServerNodeList.as_view()),
    url(r'^lpapi/server/profile_with_ip/$', views.GetRayServerNodeWithIp.as_view()),

    # for fo2 app
    url(r'^fastnet/nodes/profile/list/$', views.GetRayServerNodeList.as_view()),
    url(r'^fastnet/nodes/profile/connect/$', views.GetRayServerNodeWithIp.as_view()),

    # for admin
    url(r'^lpapi/server/report/$', report.ClientReport.as_view()),
    url(r'^lpapi/server/check/$', report.CheckReport.as_view()),
    url(r'^lpapi/server/delete/$', views_client.DeleteServer.as_view()),
    url(r'^lpapi/server/reset/$', views_client.ResetServer.as_view()),

    # for online
    url(r'^lpapi/server/online/save/$', online.SaveOnlineUser.as_view()),
    url(r'^lpapi/server/online/delete/$', online.DelOnlineUser.as_view()),
]
