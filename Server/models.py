'''
Created on Oct 6, 2021

@author: jon
'''
from admin_totals.admin import ModelAdminTotals
from django.contrib import admin
from django.db import models
from django.db.models import Sum
from django.utils import timezone
from django.utils.html import format_html


class ServerManager(models.Manager):
    pass


class Server(models.Model):
    ip = models.CharField('IP', max_length=50)
    hostname = models.CharField('Hostname', max_length=50, default='HOSTNAME')
    online_user = models.IntegerField('Online User', default=0)
    country = models.CharField('Country', blank=True, max_length=50)
    city = models.CharField('city', blank=True, max_length=64)
    enabled = models.IntegerField(default=0)
    available = models.IntegerField(default=0)
    version = models.CharField('version', max_length=20, default='v1')
    remote_error = models.CharField('remote_error', max_length=512, default='')
    created_at = models.DateTime<PERSON>ield(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    is_access_youtube = models.IntegerField('Is Access Youtube', default=0)
    is_access_tiktok = models.IntegerField('Is Access Tiktok', default=0)
    is_access_instagram = models.IntegerField('Is Access Instagram', default=0)
    is_access_facebook = models.IntegerField('Is Access Facebook', default=0)
    is_access_telegram = models.IntegerField('Is Access Telegram', default=0)

    fail_count = models.IntegerField('fail_count', default=0)

    objects = ServerManager()


@admin.register(Server)
class ServersAdmin(ModelAdminTotals):
    files = ('ip', 'enabled', 'available', 'hostname', 'online_user', 'country', 'city', 'version', 'updated_at',
             'is_access_youtube', 'is_access_tiktok', 'is_access_instagram', 'is_access_facebook',
             'is_access_telegram')
    search_fields = ('hostname', 'ip', 'country', 'version',)
    list_display = (
        'id', 'enabled_status', 'available_status', 'ip', 'hostname', 'online_user', 'country', 'city', 'version',
        'check_updated_at', 'is_access_youtube_status', 'is_access_tiktok_status', 'is_access_instagram_status',
        'is_access_facebook_status', 'is_access_telegram_status'
    )
    list_display_link = ('id',)
    list_filter = (
        'country', 'enabled', 'available', 'is_access_youtube', 'is_access_tiktok', 'is_access_instagram',
        'is_access_facebook',
        'is_access_telegram', 'city')
    list_per_page = 200
    list_totals = [('online_user', Sum)]

    objects = ServerManager()

    # 处理 enabled 字段样式
    def enabled_status(self, obj):
        if obj.enabled != 1:
            return format_html('<span style="color: red; font-weight: bold;">{}</span>', obj.enabled)
        return obj.enabled

    enabled_status.short_description = '启用状态'  # 保持原字段显示名称
    enabled_status.admin_order_field = 'enabled'  # 允许按原字段排序

    # 处理 available 字段样式
    def available_status(self, obj):
        if obj.available != 1:
            return format_html('<span style="color: red; font-weight: bold;">{}</span>', obj.available)
        return obj.available

    available_status.short_description = '可用状态'
    available_status.admin_order_field = 'available'

    def check_updated_at(self, obj):
        # 获取当前时间
        now = timezone.now()
        # 计算时间差（分钟）
        minutes_diff = (now - obj.updated_at).total_seconds() / 60
        if minutes_diff >= 10:
            # 超过10分钟，显示红色
            return format_html('<span style="color: red;">{}</span>', obj.updated_at)
        else:
            # 未超过10分钟，正常显示
            return obj.updated_at

    check_updated_at.short_description = '更新时间状态'

    # 批量处理社交平台访问状态（非1时标红）
    def _format_access_status(self, status):
        # 原逻辑：值≠1时标红；新逻辑：值=0时标红
        if int(status) == 0:
            return format_html('<span style="color: red; font-weight: bold;">{}</span>', status)
        return status

    # 各社交平台访问状态字段
    def is_access_youtube_status(self, obj):
        return self._format_access_status(obj.is_access_youtube)

    is_access_youtube_status.short_description = 'YouTube访问'
    is_access_youtube_status.admin_order_field = 'is_access_youtube'

    def is_access_tiktok_status(self, obj):
        return self._format_access_status(obj.is_access_tiktok)

    is_access_tiktok_status.short_description = 'TikTok访问'
    is_access_tiktok_status.admin_order_field = 'is_access_tiktok'

    def is_access_instagram_status(self, obj):
        return self._format_access_status(obj.is_access_instagram)

    is_access_instagram_status.short_description = 'Instagram访问'
    is_access_instagram_status.admin_order_field = 'is_access_instagram'

    def is_access_facebook_status(self, obj):
        return self._format_access_status(obj.is_access_facebook)

    is_access_facebook_status.short_description = 'Facebook访问'
    is_access_facebook_status.admin_order_field = 'is_access_facebook'

    def is_access_telegram_status(self, obj):
        return self._format_access_status(obj.is_access_telegram)

    is_access_telegram_status.short_description = 'Telegram访问'
    is_access_telegram_status.admin_order_field = 'is_access_telegram'


class OnlineUser(models.Model):
    vip = models.IntegerField('vip user')
    created_at = models.DateTimeField(auto_now_add=True)
    objects = ServerManager()
