import json
import random
import time
import uuid
from collections import deque

from Common.err import ErrInfo
from Common.views import CommonView
from Lamp import settings
from Lamp.settings import logger
from Order.common import OrderCommon
from Server.city import get_city_info
from Server.common import ServerCommon
from Server.dao.server_dao import ServerDao
from Server.models import Server
from Server.remote_control import RemoteControlServer
from Server.util import ServerUtil
from User.dao.user_dao import UserDao
from User.models import UserUsage, UsageRecord, User


class GetRayServerNodeWithIp(CommonView):

    @CommonView.VerifySign
    def post(self, request):
        try:
            data, err_code, err_msg = self.ReadPostJson(request, ('ip',))
            if err_code != 0:
                logger.error(f"[GetRayServerNodeWithIp] param error: {request.body}")
                return self.ReturnError(err_code, err_msg)

            headers = self.GetHeaderInRequest(request)
            ip = data['ip']
            net_type = data.get("net_type", "")
            node_group_name = data.get("node_group_name", "")
            device_id = headers['device_id']
            logger.info(f"[GetRayServerNodeWithIp] device_id:{device_id}, ip: {ip}, net_type:{net_type}, "
                        f"node_group_name:{node_group_name}")

            # 校验 VIP
            is_expired, expired_at = OrderCommon.is_expired(device_id)
            if is_expired:
                return self.ReturnError(ErrInfo.JUMP_VIP_VIEW)

            # 判断用户的流量别太过分
            if UserDao.is_hit_traffic_limit(device_id):
                logger.error(f"[GetRayServerNodeWithIp] user traffic limit reached, device_id: {device_id}")
                return self.ReturnError(ErrInfo.NO_SERVER_TO_USE)

            # 获取用户，确保有 ray_id
            user = self._get_or_create_ray_user(device_id)

            # 分配服务器（内部包含自动换 IP 的逻辑）
            server, ip = self._assign_server(device_id, ip, user.ray_id)
            if not server:
                logger.error(f"[GetRayServerNodeWithIp] device_id:{device_id}, ip: {ip} assign failed")
                return self.ReturnError(ErrInfo.NO_SERVER_TO_USE)

            # 更新 usage + 记录
            self._save_usage_and_record(device_id, user.ray_id, server, ip, net_type, node_group_name)

            # 构建响应
            rsp = self._build_response(ip, user.ray_id)
            logger.info(f"[GetRayServerNodeWithIp] device_id:{device_id}, ip: {ip} assign success, rsp: {rsp}")
            encrypt_info = ServerUtil.AESEncode(json.dumps(rsp))
            return self.ReturnSuccess(encrypt_info)

        except Exception:
            logger.error("[GetRayServerNodeWithIp] failed", exc_info=True)
            return self.ReturnError(ErrInfo.SERVER_ERROR)

    def _get_or_create_ray_user(self, device_id):
        user = User.objects.filter(device_id=device_id).first()
        if not user.ray_id:
            user.ray_id = str(uuid.uuid4())
            user.save()
        return user

    def _assign_server(self, device_id, ip, ray_id):
        """
        负责真正的 server 分配逻辑
        1. 优先尝试请求的 ip
        2. 如果找不到/分配失败 -> 自动换 ip
        3. 确保分配成功后，server.online_user += 1
        """
        server = ServerDao.get_valid_server_by_ip(ip)
        if not server:
            logger.warning(f"[GetRayServerNodeWithIp] device_id:{device_id}, ip: {ip} not found, try auto switch")
            return self._assign_with_auto_switch(device_id, ray_id, [ip])

        # 尝试分配
        err = RemoteControlServer().AddUserWithRay(ip, ray_id)
        if err:
            logger.warning(f"[GetRayServerNodeWithIp] assign failed on ip:{ip}, try auto switch")
            return self._assign_with_auto_switch(device_id, ray_id, [ip])

        # 成功分配
        server.online_user += 1
        server.save()
        return server, ip

    def _assign_with_auto_switch(self, device_id, ray_id, exclude_ips, max_retry=3):
        """
        自动切换 IP，最多尝试 max_retry 次
        """
        tried = 0
        while tried < max_retry:
            tried += 1
            info = ServerCommon.get_server_by_country_with_ray_without_ips('US', device_id, exclude_ips)
            if not info:
                logger.error(f"[GetRayServerNodeWithIp] no candidate server found on retry {tried}")
                return None, None

            ip = info['ip']
            exclude_ips.append(ip)  # 避免重复
            server = ServerDao.get_valid_server_by_ip(ip)
            if not server:
                logger.warning(f"[GetRayServerNodeWithIp] candidate ip:{ip} invalid, retry {tried}")
                continue

            err = RemoteControlServer().AddUserWithRay(ip, ray_id)
            if err:
                logger.warning(f"[GetRayServerNodeWithIp] assign failed on ip:{ip}, retry {tried}")
                continue

            # 分配成功
            server.online_user += 1
            server.save()
            logger.info(f"[GetRayServerNodeWithIp] auto switch success on retry {tried}, ip:{ip}, device:{device_id}")
            return server, ip

        logger.error(f"[GetRayServerNodeWithIp] all {max_retry} retries failed for device:{device_id}")
        return None, None

    def _save_usage_and_record(self, device_id, ray_id, server, ip, net_type, node_group_name):
        usage = self.GetOrNone(UserUsage, device_id=device_id)
        if usage is None:
            try:
                usage = UserUsage(device_id=device_id, account_id='', ip=ip, country=server.country,
                                  password='', ray_id=ray_id)
                usage.save()
            except Exception as e:
                if "Duplicate entry" not in str(e):
                    raise e
        else:
            usage.ray_id = ray_id
            usage.country = server.country
            usage.password = ''
            usage.account_id = ''
            usage.ip = ip
            usage.save()

        record = UsageRecord(
            device_id=device_id, account_id="", ip=ip, country=server.country,
            password="", ray_id=ray_id, req_country=server.country,
            net_type=net_type, node_group_name=node_group_name
        )
        record.save()

    def _build_response(self, ip, ray_id):
        return {
            'server': ip,
            'port': 443,
            'type': 'vmess',
            'uuid': ray_id,
            'alterId': 0,
            'cipher': 'auto',
            'tls': True,
            'skip-cert-verify': True,
            'network': 'ws',
            'path': '/user',
            'host': 'api.gongbaojiding.xyz',
            'udp': True
        }


class GetRayServerNodeList(CommonView):

    @CommonView.VerifySign
    def post(self, request):
        start_all = time.time()
        try:
            # === 阶段 0: 参数和日志 ===
            data, err_code, err_msg = self.ReadPostJson(request, ())
            if err_code != 0:
                logger.error(f"[GetRayServerNodeList] invalid param:{request.body}")
                return self.ReturnError(err_code, err_msg)

            headers = self.GetHeaderInRequest(request)
            device_id = headers['device_id']
            app_version = headers["app_version"]
            lang = headers.get('lang', 'ru')  # 默认俄语
            logger.info(f"[GetRayServerNodeList] device_id:{device_id}, lang:{lang}")

            # 判断用户的流量别太过分
            if UserDao.is_hit_traffic_limit(device_id):
                logger.error(f"[GetRayServerNodeList] user traffic limit reached, device_id: {device_id}")
                return self.ReturnError(ErrInfo.NO_SERVER_TO_USE)

            # === 阶段 1: DB 查询 ===
            servers = (
                Server.objects
                .filter(enabled=1, online_user__lt=15)
                .only("ip", "city", "country",
                      "is_access_youtube", "is_access_instagram",
                      "is_access_facebook", "is_access_tiktok",
                      "is_access_telegram")
                .order_by("online_user")
            )

            # === 阶段 2&3: US 网站分组 ===
            used_ips = set()
            city_info_cache = {}
            result = self._build_us_platform_groups(servers, used_ips, city_info_cache, lang, app_version)

            # === 阶段 4: US 按城市 + 非US按国家 ===
            result.extend(
                self._build_country_groups(servers, used_ips, city_info_cache, lang, app_version)
            )

            logger.info(f"[GetRayServerNodeList] device_id:{device_id}, lang:{lang}, "
                        f"result size:{len(result)}, total:{int((time.time() - start_all) * 1000)}ms")
            return self.ReturnSuccess({"node_list": result})

        except Exception:
            logger.error("[GetRayServerNodeList] failed", exc_info=True)
            return self.ReturnError(ErrInfo.SERVER_ERROR)

    # ================= 辅助方法 =================

    @staticmethod
    def _get_city_info_cached(cache, city, lang):
        key = (city, lang)
        if key not in cache:
            cache[key] = get_city_info(city, lang)
        return cache[key]

    def _build_us_platform_groups(self, servers, used_ips, cache, lang, app_version):
        """阶段 2+3: 构建美国地区的平台分组 (YouTube/FB/IG/TT/TG)"""
        groups_data = {
            "youtube": deque([s for s in servers if (s.is_access_youtube == 1 and s.country == "US")]),
            "instagram": deque([s for s in servers if (s.is_access_instagram == 1 and s.country == "US")]),
            "facebook": deque([s for s in servers if (s.is_access_facebook == 1 and s.country == "US")]),
            "tiktok": deque([s for s in servers if (s.is_access_tiktok == 1 and s.country == "US")]),
            "telegram": deque([s for s in servers if (s.is_access_telegram == 1 and s.country == "US")]),
        }

        picked_groups = {k: [] for k in groups_data}
        result = []

        for _ in range(settings.SERVERS_MAX_PER_GROUP):
            made_progress = False
            for group_name, group_list in groups_data.items():
                if len(picked_groups[group_name]) >= settings.SERVERS_MAX_PER_GROUP:
                    continue
                while group_list:
                    server = group_list.popleft()
                    if server.ip in used_ips:
                        continue
                    city_info = self._get_city_info_cached(cache, server.city, lang)
                    title = (f"{group_name.capitalize()} - {city_info['city']}"
                             if city_info else f"{group_name.capitalize()} - {server.country}")

                    v = {
                        "flag": f"https://cdn.gongbaojiding.xyz/flag/{server.country}.png",
                        "speed_ms": f"{random.randint(100, 500)} ms",
                        "title": title,
                        "ip": server.ip,
                        "country": server.country,
                    }
                    if app_version < 1010100:
                        v["name_en"] = title
                    picked_groups[group_name].append(v)

                    used_ips.add(server.ip)
                    made_progress = True
                    break
            if not made_progress:
                break

        for k, v in picked_groups.items():
            if v:
                v = sorted(v, key=lambda x: (-1 if x['country'] == "US" else 0))
                for i in v:
                    i.pop("country", None)
                result.append({"name": k.capitalize(), "nodes": v})
        return result

    def _build_country_groups(self, servers, used_ips, cache, lang, app_version):
        """阶段 4: US 按 city，非US 按 country"""
        us_groups, country_groups = self._split_servers_by_region(servers, used_ips)

        result = []
        result.extend(
            self._build_us_nodes(us_groups, used_ips, cache, lang, app_version,
                                 settings.SERVERS_MAX_PER_US_CITY)
        )
        result.extend(
            self._build_non_us_nodes(country_groups, used_ips, cache, lang, app_version,
                                     settings.SERVERS_MAX_PER_COUNTRY)
        )

        # 仅打乱外层列表的顺序，子列表内部元素顺序保持不变
        random.shuffle(result)

        return result

    @staticmethod
    def _split_servers_by_region(servers, used_ips):
        """把服务器分成 US 城市组和非US国家组"""
        us_groups = {}
        country_groups = {}
        for s in servers:
            if s.ip in used_ips:
                continue
            if s.country == "US":
                us_groups.setdefault(f"{s.country}_{s.city}", []).append(s)
            else:
                country_groups.setdefault(s.country, []).append(s)
        return us_groups, country_groups

    def _build_us_nodes(self, us_groups, used_ips, cache, lang, app_version, max_per_us_city):
        """US 按城市分组"""
        result = []
        us_keys = list(us_groups.keys())
        random.shuffle(us_keys)
        for k in us_keys:
            nodes_rsp = []
            country_name, city_name = k.split("_", 1)
            city_info = self._get_city_info_cached(cache, city_name, lang)
            if not city_info:
                continue
            for s in us_groups[k]:
                if len(nodes_rsp) >= max_per_us_city:
                    break
                if s.ip in used_ips:
                    continue
                title = f"{city_info['city']} - {len(nodes_rsp) + 1}"
                v = {
                    "flag": f"https://cdn.gongbaojiding.xyz/flag/{country_name}.png",
                    "speed_ms": f"{random.randint(100, 500)} ms",
                    "title": title,
                    "ip": s.ip,
                }
                if app_version < 1010100:
                    v["name_en"] = title
                nodes_rsp.append(v)
                used_ips.add(s.ip)
            if nodes_rsp:
                result.append({
                    "name": f"{city_info['country']} - {city_info['city']}",
                    "nodes": nodes_rsp
                })
        return result

    def _build_non_us_nodes(self, country_groups, used_ips, cache, lang, app_version, max_per_country):
        """非US 按国家分组"""
        result = []
        country_keys = list(country_groups.keys())
        random.shuffle(country_keys)
        for c in country_keys:
            nodes_rsp = []
            nodes = country_groups[c]
            random.shuffle(nodes)

            # 尝试获取本地化的国家显示名
            ci = None
            if nodes and nodes[0].city:
                ci = self._get_city_info_cached(cache, nodes[0].city, lang)
            country_display = ci.get("country") if ci else None

            for s in nodes:
                if len(nodes_rsp) >= max_per_country:
                    break
                if s.ip in used_ips:
                    continue
                title = f"{country_display or s.country} - {len(nodes_rsp) + 1}"
                v = {
                    "flag": f"https://cdn.gongbaojiding.xyz/flag/{c}.png",
                    "speed_ms": f"{random.randint(100, 500)} ms",
                    "title": title,
                    "ip": s.ip,
                }
                if app_version < 1010100:
                    v["name_en"] = title
                nodes_rsp.append(v)
                used_ips.add(s.ip)
            if nodes_rsp:
                result.append({
                    "name": country_display or c,
                    "nodes": nodes_rsp
                })
        return result
