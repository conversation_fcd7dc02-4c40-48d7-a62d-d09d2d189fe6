import time

import requests

from Lamp import settings
from Lamp.settings import logger
from Server.dao.server_dao import ServerDao


class RemoteControlServer:

    def __do_post(self, url, data):

        data['token'] = settings.SWAN_ACCESS_TOKEN
        rsp_content = ""
        rsp_code = ""
        try:
            for _ in range(0, 3):
                r = requests.post(url, json=data, timeout=10)
                rsp_content = r.content
                rsp_code = r.status_code
                logger.info(
                    f"RemoteControlServer __do_post, url: {url}, data: {data}, code:{rsp_code},res: {r.content}")
                if r.status_code == 200:
                    res = r.json()
                    return res.get('error')
        except Exception as e:
            logger.error(f"__do_post error: {e}", exc_info=True)

        logger.error(
            f"RemoteControlServer __do_post, url: {url}, data: {data}, code:{rsp_code}, res: {rsp_content}")
        return f'server error: {rsp_code}:{rsp_content}'

    def __do_post_ray_server(self, url, data):
        start_time = time.time()
        data['token'] = settings.SWAN_ACCESS_TOKEN
        rsp_content = ""
        rsp_code = ""
        for _ in range(0, 3):
            try:
                r = requests.post(url, json=data, timeout=10)
                rsp_content = r.content
                rsp_code = r.status_code
                logger.info(f"[RemoteControlServer __do_post_ray_server], url: {url}, data: {data}, code:{rsp_code},"
                            f"res: {r.content}")
                if r.status_code == 200:
                    content_json = r.json()
                    if "error" in content_json:
                        logger.warning(
                            f"[RemoteControlServer __do_post_ray_server], url: {url}, data: {data}, code:{rsp_code},"
                            f"error res: {r.content}, cost:{int((time.time() - start_time) * 1000)} ms")
                        return content_json["error"]

                    err_code = content_json['error_code']
                    if err_code == 0:
                        logger.info(
                            f"[RemoteControlServer __do_post_ray_server], url: {url}, data: {data}, code:{rsp_code},"
                            f"res: {r.content}, cost:{int((time.time() - start_time) * 1000)} ms")
                        return ""
                    return rsp_content
                time.sleep(2)
            except Exception as e:
                logger.warning(f"__do_post error: {e}", exc_info=True)
                time.sleep(2)

        logger.warning(f"[RemoteControlServer __do_post_ray_server], url: {url}, data: {data}, "
                       f"status_code:{rsp_code}, res: {rsp_content}, cost:{int((time.time() - start_time) * 1000)} ms")
        return f'server error: {rsp_code}:{rsp_content}'

    def __update_server_remote_result(self, server_ip: str, error: str):
        ServerDao.update_err_by_ip(server_ip, error)

    def AddUser(self, remote_server_ip, name, password):

        url = 'http://%s:8000/user/add/' % remote_server_ip
        error = self.__do_post(url, {"name": name, "password": password})
        self.__update_server_remote_result(remote_server_ip, error)
        return error

    def AddUserWithRay(self, remote_server_ip: str, user_ray_id: str) -> str:

        url = 'http://%s:8123/user/add/' % remote_server_ip
        error = self.__do_post_ray_server(url, {"ids": [user_ray_id]})
        self.__update_server_remote_result(remote_server_ip, error)
        return error

    def DelUsers(self, remote_server_ip, name_list):

        url = 'http://%s:8000/user/del/' % remote_server_ip
        error = self.__do_post(url, {"names": name_list})
        self.__update_server_remote_result(remote_server_ip, error)
        return error

    def DelUsersWithRay(self, remote_server_ip: str, user_ray_id: str) -> str:

        url = 'http://%s:8123/user/del/' % remote_server_ip
        error = self.__do_post_ray_server(url, {"ids": [user_ray_id]})
        self.__update_server_remote_result(remote_server_ip, error)
        return error

    def BatchDelUsersWithRay(self, remote_server_ip: str, user_ray_id_list: list) -> str:

        url = 'http://%s:8123/user/del/' % remote_server_ip
        error = self.__do_post_ray_server(url, {"ids": user_ray_id_list})
        self.__update_server_remote_result(remote_server_ip, error)
        return error

    def DelGuestByGroup(self):

        # todo: fixme
        pass

    def Reset(self, remote_server_ip):

        url = 'http://%s:8123/reset/' % remote_server_ip
        error = self.__do_post(url, data={})
        self.__update_server_remote_result(remote_server_ip, error)
        return error
