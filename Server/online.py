"""
Created on 2021/11/17

@author: jon
"""
from datetime import <PERSON><PERSON><PERSON>

from django.db.models import Sum
from django.shortcuts import render

from Common.timeutil import TimeUtil
from Common.views import CommonView
from Lamp.settings import logger
from Server.models import OnlineUser, Server
from Server.dao.server_dao import ServerDao


class SaveOnlineUser(CommonView):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.token = 'fkwA11'

    def get(self, request):

        try:
            data, err_code, err_msg = self.GetDataInGet(request, ('token',))
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            if data['token'] != self.token:
                return self.ReturnSuccess({"error": "wrong token"})

            online_cnt = Server.objects.filter(enabled=1).aggregate(total=Sum('online_user')).get('total')
            logger.info(f"[SaveOnlineUser] save online cnt: {online_cnt}")
            if not online_cnt:
                online_cnt = 0

            t = OnlineUser(
                vip=online_cnt
            )
            t.save()

            return self.ReturnSuccess()
        except Exception:
            logger.error("SaveOnlineUser error", exc_info=True)
            return self.ReturnError(1, "SaveOnlineUser error")


class DelOnlineUser(CommonView):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.token = 'fkwA11'

    def get(self, request):

        try:
            data, err_code, err_msg = self.GetDataInGet(request, ('token',))
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            if data['token'] != self.token:
                return self.ReturnSuccess({"error": "wrong token"})

            ts_before = TimeUtil.GetNow() - timedelta(days=90)
            OnlineUser.objects.filter(created_at__lte=ts_before).delete()
            logger.info(f"[DelOnlineUser] del before: {ts_before}")

            return self.ReturnSuccess()
        except Exception:
            logger.error("DelOnlineUser error", exc_info=True)
            return self.ReturnError(1, "DelOnlineUser error")


class ShowOnline(CommonView):

    def get(self, request):
        try:

            N = 1500
            data = OnlineUser.objects.order_by('-id')[0: N]
            ts = [x.get('created_at').strftime('%m/%d %H:%M') for x in data.values('created_at')]
            vip = [int(x.get('vip')) for x in data.values('vip')]

            ts.reverse()
            vip.reverse()

            context = {
                'ts': ts,
                'vip': vip,
                'server_vip': ServerDao.get_all_enabled_servers_cnt(),
            }

            return render(request, 'online.html', context)
        except Exception:
            logger.error("ShowOnline error", exc_info=True)
            return self.ReturnError(1, "ShowOnline error")
