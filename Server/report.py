"""
Created on 2021/10/12

@author: jon
"""
from datetime import datetime, timedelta

from Common.views import CommonView
from Lamp import settings
from Lamp.settings import logger
from Server.models import Server
from Server.dao.server_dao import ServerDao


class ClientReport(CommonView):

    ## /server/report/ {key, ip, hostname, online, country, version}
    def post(self, request):

        data, _, _ = self.ReadPostJson(request)
        logger.info(f"[ClientReport] parma: {data}")

        if data.get('key', None) != settings.REPORT_KEY:
            logger.error(f"[ClientReport] incorrect key: {data.get('key', None)}")
            return self.ReturnJson({"err": "incorrect key"})

        server = ServerDao.get_server_by_ip_without_condition(data['ip'])
        if server is None:
            server = Server(
                ip=data['ip'],
                hostname=data['hostname'],
                online_user=data['online'],
                country=data['country'],
                enabled=1,
                available=1,
                version=data.get('version'),
                city=data.get('city', '')
            )
        else:
            server.hostname = data['hostname']
            server.online_user = data['online']
            server.country = data['country']
            server.city = data.get('city', '')
            server.version = data.get('version', '')
            server.enabled = 1

        server.save()
        return self.ReturnJson({"status": "ok"})


# todo: fix me
# check server offline if no report status update
class CheckReport(CommonView):

    # /server/check/
    def get(self, request):
        
        servers = ServerDao.get_all_enabled_servers()
        for server in servers:
            if datetime.utcnow() - server.updated_at > timedelta(minutes=10):
                server.enabled = 0
                server.save()

        return self.ReturnStr("ok")
