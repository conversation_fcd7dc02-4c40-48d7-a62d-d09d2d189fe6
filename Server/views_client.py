import threading

from Common.err import ErrInfo
from Common.views import CommonView
from Lamp import settings
from Lamp.settings import logger
from Server.remote_control import RemoteControlServer
from Server.dao.server_dao import ServerDao


class DeleteServer(CommonView):

    def post(self, request):
        i = 0
        try:
            data, _, _ = self.ReadPostJson(request)

            if data.get('key', None) != settings.REPORT_KEY:
                return self.ReturnJson({"err": "incorrect key"})

            for s in data['droplets']:
                t = ServerDao.get_server_by_hostname(s)
                if len(t) > 0:
                    t[0].delete()
                    i += 1
        except Exception:
            logger.error("delete server failed", exc_info=True)

        return self.ReturnStr(f"deleted {i} hosts")


class ResetServer(CommonView):

    @staticmethod
    def do_reset():
        try:
            servers = ServerDao.get_all_enabled_servers()
            logger.info(f"reset servers: total count={servers.count()}")
            for s in servers:
                err = RemoteControlServer().Reset(s.ip)
                if err:
                    logger.error(f"reset server {s.ip}, err={err}")
                else:
                    logger.info(f"reset server {s.ip}, err={err}")
                s.remote_error = err
                s.save()
        except Exception:
            logger.error("reset server failed", exc_info=True)

    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, ('token',))
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        if data['token'] != 'watch-dog-v001':
            logger.error(f"reset server token invalid: {data['token']}")
            return self.ReturnError(ErrInfo.AUTH_TOKEN_INVALID)

        # 未来可能很多台服务器
        t = threading.Thread(target=self.do_reset, args=())
        t.start()

        return self.ReturnSuccess()
