"""
Created on 2021/10/28

@author: jon
"""
import base64

from Crypto.Cipher import AES

from Lamp import settings


class ServerUtil:

    @staticmethod
    def AESEncode(content: str) -> str:
        cipher = AES.new(
            settings.AES_KEY.encode(encoding='utf8'),
            AES.MODE_CFB,
            iv=settings.AES_IV.encode(encoding='utf8'),
            segment_size=128
        )

        encrypt_data = cipher.encrypt(content.encode(encoding='utf8'))
        return base64.b64encode(encrypt_data).decode(encoding='utf8')

    @staticmethod
    def AESDecode(content: str) -> str:
        cipher = AES.new(
            settings.AES_KEY.encode(encoding='utf8'),
            AES.MODE_CFB,
            iv=settings.AES_IV.encode(encoding='utf8'),
            segment_size=128
        )

        original_data = cipher.decrypt(base64.b64decode(content))
        return original_data.decode(encoding='utf8')


if __name__ == '__main__':
    data = 'this is my secret msg'

    print(ServerUtil.AESEncode(data))

    print(ServerUtil.AESDecode(
        'EJga1H7kpx6ErhGQrK+5EHgpmzLBlcnDEnIO6WD76cNIBHrjbCB1sdwNcBHrGRsPNmScUv1mBIOspAD6zmdWpTpcm2S2JecxMoTlBWTTLjYVIi9J/bruc4atbPvCKkEaYCLkxP8Q7BtUuvI6gN60nM+JjKNYIjpNnmmnl23bUidKLiRXO4/60z/ygXpudL8='))
