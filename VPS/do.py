import requests
import time

class DO:


    def __init__(self):

        self.api_key = '***********************************************************************'
        self.snapshot_id = ''
        self.ssh_keys = []
        self.base_url = "https://api.digitalocean.com"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
        }


    def get_all_instances(self):

        r = requests.get(f'{self.base_url}' + '/v2/droplets?per_page=100', headers=self.headers)
        return r.json()['droplets']


    def delete_instance_by_hostname(self, hostname):

        instance_id = None
        for i in self.get_all_instances():
            if i['name'] == hostname:
                instance_id = i['id']
                break
        
        if instance_id is None:
            return 'instance not found'

        print('get instance id, %s -> %s' % (hostname, instance_id) )

        r = requests.delete(f'{self.base_url}' + '/v2/droplets/' + str(instance_id), headers=self.headers)
        return r.content



def delete_node_and_server(hostname):
    print('deleting %s' % hostname)
    do = DO()
    try:
        print(do.delete_instance_by_hostname(hostname))
    except Exception as e:
        print('DO delete error: %s' % e)

    time.sleep(5)
    data = {
        'key': 'XNsj^Nbsj72jAn-xYwuiUKS_xMMZ',
        'droplets': [hostname]
    }
    r = requests.post('https://api.gongbaojiding.xyz/lpapi/server/delete/', json=data)
    print(r.content)



if __name__ == '__main__':

    do = DO()
    # print(do.get_all_instances())
    # print(do.get_all_snapshots())

    # print(do.delete_instance_by_hostname('ray-us-21'))

    VPS = [
        'v-us-sf-8-2025081717'
    ]

    for vps in VPS:
        delete_node_and_server(vps)