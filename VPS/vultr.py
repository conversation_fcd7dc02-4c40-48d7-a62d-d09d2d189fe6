import requests, random, time
from datetime import datetime

ACCOUNT_INFO = {
    'OY':   {
            'token': 'LRN2LEAFNQ6PFA523HPOK7ND7KTE7WY5XD2A', 
            'snapshot_id': 'b87fd9ff-162d-482a-9f42-4d2d54ac80a6',
            'ssh_keys': ['14ea4978-7a56-4a4d-8fa5-f14af7464e91', '6b974fdc-dc8d-4ac9-8091-79e8f3faa001', '4744639c-45de-4296-9884-dfaaaba2bcdd', '43d54b1e-de2c-4655-a1bd-128551cd2b36', '78e24832-a00a-4422-a168-d298ba242698']
        },
    'JJ': {
            'token': '3ODQTA4EOMQRCVRXQY4FHDXZGJ2XRO5DOURQ', 
            'snapshot_id': '8b6fc1c9-3527-429c-bb9a-d1f5c1af0c8f',
            'ssh_keys': ['cc163191-2233-47a5-9b23-6ab914f84ec5', '9a70b060-c10d-41dd-9dce-c396614cf3f2']
        },
    'RC': {
            'token': '5SHWCMSDCPXFCAABZPKFTZTKTBOSPPG3NI3Q',
            'snapshot_id': '80b23e15-2a37-4f40-99fe-0274a9976e8a',
            'ssh_keys': ['02c414a3-2b7c-400f-b612-850c79c2d9a8', '26a991c2-d054-472f-8cbd-b3d693268d09']
        },
    'KM': {
            'token': 'KLMV7BAOZLAS5Q6R25IUIDEMJZOUS5CU4CDQ',
            'snapshot_id': '61d56840-31c3-412e-92cc-ef36d7f904d6',
            'ssh_keys': ['27b4f442-7dbd-4765-a283-48375fa34425', '27b7f5bc-f981-4c97-b0b6-7ceff858e14e']
    }
}

'''
{'id': 'nrt', 'city': 'Tokyo', 'country': 'JP'},
{'id': 'itm', 'city': 'Osaka', 'country': 'JP'},
{'id': 'fra', 'city': 'Frankfurt', 'country': 'GE'},
{'id': 'icn', 'city': 'Seoul', 'country': 'KR'},
{'id': 'cdg', 'city': 'Paris', 'country': 'FR'},
{'id': 'lhr', 'city': 'London', 'country': 'GB'},
{'id': 'yto', 'city': 'Toronto', 'country': 'CA'},
{'id': 'man', 'city': 'Manchester', 'country': 'GB'},
{'id': 'sgp', 'city': 'Singapore', 'country': 'SGP'},
{'id': 'syd', 'city': 'Sydney', 'country': 'AU'},
{'id': 'atl', 'city': 'Atlanta', 'country': 'US'},
{'id': 'dfw', 'city': 'Dallas', 'country': 'US'},
{'id': 'lax', 'city': 'Los Angeles', 'country': 'US'},
{'id': 'ord', 'city': 'Chicago', 'country': 'US'},
{'id': 'sjc', 'city': 'Silicon Valley', 'country': 'US'},
{'id': 'sea', 'city': 'Seattle', 'country': 'US'},
{'id': 'ewr', 'city': 'New Jersey', 'country': 'US'},
{'id': 'mia', 'city': 'Miami', 'country': 'US'}
'''


REGION = {
    'JP': ['nrt', 'itm'],
    'GE': ['fra'],
    'KR': ['icn'],
    'FR': ['cdg'],
    'GB': ['lhr', 'man'],
    'CA': ['yto'],
    'SGP': ['sgp'],
    'US': ['atl', 'dfw', 'lax', 'ord', 'sjc', 'sea', 'ewr', 'mia'],
    'AU': ['syd']
}



class Vultr:


    def __init__(self, account_info):

        self.api_key = account_info['token']
        self.snapshot_id = account_info['snapshot_id']
        self.ssh_keys = account_info['ssh_keys']
        self.base_url = "https://api.vultr.com/v2"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
        }


    def get_all_instances(self):

        url = self.base_url + '/instances'
        response = requests.get(url, headers=self.headers, timeout=15)
        # print(response.json())
        return response.json()['instances']

    def get_all_regions(self):

        url = self.base_url + '/regions'
        response = requests.get(url, headers=self.headers, timeout=15)
        return response.json()['regions']


    def get_all_snapshots(self):

        url = self.base_url + '/snapshots'
        response = requests.get(url, headers=self.headers, timeout=15)
        return response.json()['snapshots']


    def get_instance_id_by_hostname(self, hostname):
        instance_id = None
        for i in self.get_all_instances():
            # print(i)
            if i['hostname'] == hostname:
                instance_id = i['id']
                return instance_id
        
        if instance_id is None:
            return 'instance not found'
        

    def delete_instance_by_hostname(self, hostname):

        instance_id = self.get_instance_id_by_hostname(hostname)
        
        print('get instance id, %s -> %s' % (hostname, instance_id) )
        url = self.base_url + '/instances/' + instance_id
        response = requests.delete(url, headers=self.headers, timeout=15)
        return '%s deleted success' % hostname

    def get_sshkeys(self):

        url = self.base_url + '/ssh-keys'
        response = requests.get(url, headers=self.headers, timeout=15)
        return response.json()['ssh_keys']

    # input hostname: v-us-100, v-us-atl-100, v-us-atl-100-**********
    # gen hostname: v-us-atl-100-**********
    def create_instance_with_snapshot(self, hostname):

        country = hostname.split('-')[1].upper()

        if len(hostname.split('-')) < 5:
            host_id = str(hostname.split('-')[-1])
        else:
            host_id = str(hostname.split('-')[-2])
        

        if len(hostname.split('-')) > 3:
            region_id = hostname.split('-')[2]
        else:
            region_ids = REGION[country]
            region_id = region_ids[random.randint(0, len(region_ids)-1)]

        print('creating %s - %s - %s' % (hostname, country, region_id))

        hostname_new = 'v-%s-%s-%s-%s' % (country.lower(), region_id, host_id, datetime.now().strftime('%Y%m%d%H'))

        data = {
            'region': region_id,
            'snapshot_id': self.snapshot_id,
            'hostname': hostname_new,
            'label': hostname_new,
            'plan': 'vc2-1c-1gb',
            'sshkey_id': self.ssh_keys,
            'backups': 'disabled'
        }

        print(data)
        url = self.base_url + '/instances'
        resp = requests.post(url, json=data, headers=self.headers, timeout=15)
        print(resp.content)
        return resp.json()

    def reboot_hosts(self, hostname):
        url = self.base_url + '/instances/reboot'
        instance_id = self.get_instance_id_by_hostname(hostname)
        print('rebooting instance:', hostname, '->', instance_id)
        resp = requests.post(url, headers=self.headers, timeout=15, json={'instance_ids': [instance_id]})
        print(resp.content)
        return 'success'


# input hostname: v-us-100, v-us-atl-100, v-us-atl-100-**********
def which_account(hostname):

    if len(hostname.split('-')) < 5:
        host_id = int(hostname.split('-')[-1])
    else:
        host_id = int(hostname.split('-')[-2])

    if host_id >= 400:
        return 'KM'
    if host_id >= 300:
        return 'JJ'
    if host_id >= 200:
        return 'RC'
    if host_id >= 100:
        return 'OY'
    return None



def delete_node_and_server(hostname):
    print('deleting %s' % hostname)
    data = {
        'key': 'XNsj^Nbsj72jAn-xYwuiUKS_xMMZ',
        'droplets': [hostname]
    }
    r = requests.post('https://api.gongbaojiding.xyz/lpapi/server/delete/', json=data)


    account = which_account(hostname)
    v = Vultr(ACCOUNT_INFO[account])
    try:
        print(v.delete_instance_by_hostname(hostname))
    except Exception as e:
        print('vultr delete error: %s' % e)

    time.sleep(5)
    r = requests.post('https://api.gongbaojiding.xyz/lpapi/server/delete/', json=data)
    print(r.content)


# input hostname: v-us-100, v-us-atl-100, v-us-atl-100-**********
def create_instance_with_hostname(hostname):
    account = which_account(hostname)
    if account is None:
        print('account is None')
        return
    v = Vultr(ACCOUNT_INFO[account])
    print(f'create instance with hostname={hostname}, account={account}')
    v.create_instance_with_snapshot(hostname)



if __name__ == '__main__':

    # v = Vultr(ACCOUNT_INFO['KM'])
    # instances = v.get_all_instances()
    # for i in instances:
    #     print(i)
    #     print('-' * 20)

    # for i in v.get_sshkeys():
    #     print(i)

    # print(v.delete_instance_by_hostname('ray-us-105'))

    # print(v.create_instance_with_snapshot('ray-fr-302', 'FR'))

    # print(which_account('ray-us-2'))

    VPS = [
        'v-us-ord-311-**********',
    ]

    for vps in VPS:
        delete_node_and_server(vps)
    #     # account.reboot_hosts(vps)
    #     # account.create_instance_with_snapshot(vps)

    # time.sleep(30)

    # VPS = [
    #     'v-fr-302',
    # ]

    # for vps in VPS:
    #     create_instance_with_hostname(vps)

