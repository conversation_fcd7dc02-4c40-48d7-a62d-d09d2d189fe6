# 专门处理收入情况的工具包
from datetime import timedelta, datetime

from django.db import connection

from Lamp.settings import logger


class IncomeHelper:
    def __init__(self):
        self.income_total_types = (
            '总收入', '总VIP收入', '总周度VIP收入', '总月度VIP收入', '总季度VIP收入', '总年度VIP收入',)
        self.income_total_user_cnt_types = ('总用户数', '总VIP用户数', '总白嫖用户数', '总退款用户数',)
        self.income_today_types = ('今日收入', '今日VIP收入', '今日月度VIP收入', '今日周度VIP收入',)
        self.today = datetime.utcnow()
        self.begin = self.today - timedelta(days=30)
        self.tmr = self.today + timedelta(days=1)
        self.days = self.__init_days()

    def __init_days(self):

        days = []

        for i in range(30, -1, -1):
            dt = self.today - timedelta(days=i)
            days.append(dt.strftime("%Y-%m-%d"))

        return days

    def __init_days_dict(self):

        return dict([x, 0] for x in self.days)

    def get_income_total_data(self):
        data = {}
        for t in self.income_total_types:
            data[t] = self.__init_days_dict()

        sql = f'''
        select day,
         sum(total_income) as total_income, sum(total_vip_income) as total_vip_income, 
         sum(total_vip_week_income) as total_vip_week_income,
         sum(total_vip_month_income) as total_vip_month_income, 
         sum(total_vip_season_income) as total_vip_season_income, 
         sum(total_vip_year_income) as total_vip_year_income
         from Report_finance where day between '{self.begin.strftime('%Y-%m-%d')}' and '{self.tmr.strftime('%Y-%m-%d')}'  group by day
        '''
        logger.info(f"report sum incoming data: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, total_income, total_vip_income, total_vip_week_income,
                 total_vip_month_income, total_vip_season_income, total_vip_year_income) in cur.fetchall():
                logger.info(f"report sum {total_income}, {total_vip_income} {total_vip_month_income}")
                data['总收入'][day] = round(total_income, 2)
                data['总VIP收入'][day] = round(total_vip_income, 2)
                data['总周度VIP收入'][day] = round(total_vip_week_income, 2)
                data['总月度VIP收入'][day] = round(total_vip_month_income, 2)
                data['总季度VIP收入'][day] = round(total_vip_season_income, 2)
                data['总年度VIP收入'][day] = round(total_vip_year_income, 2)

        return data

    def get_income_total_data_user_cnt(self):
        data = {}
        for t in self.income_total_user_cnt_types:
            data[t] = self.__init_days_dict()

        sql = f'''
        select day,
         sum(total_user_cnt) as total_user_cnt, sum(total_vip_user_cnt) as total_vip_user_cnt, sum(total_baipiao_user_cnt) as total_baipiao_user_cnt, sum(total_refund_user_cnt) as total_refund_user_cnt
         from Report_finance where day between '{self.begin.strftime('%Y-%m-%d')}' and '{self.tmr.strftime('%Y-%m-%d')}'  group by day
        '''
        logger.info(f"report sum incoming data: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, total_user_cnt, total_vip_user_cnt, total_baipiao_user_cnt,
                 total_refund_user_cnt) in cur.fetchall():
                data['总用户数'][day] = int(total_user_cnt)
                data['总VIP用户数'][day] = int(total_vip_user_cnt)
                data['总白嫖用户数'][day] = int(total_baipiao_user_cnt)
                data['总退款用户数'][day] = int(total_refund_user_cnt)

        return data

    def get_income_data_today(self):
        data = {}
        for t in self.income_today_types:
            data[t] = self.__init_days_dict()

        sql = f'''
        select day, sum(today_total_income) as today_total_income,
         sum(today_vip_income) as today_vip_income,
         sum(today_vip_week_income) as today_vip_week_income,
          sum(today_vip_month_income) as today_vip_month_income
         from Report_finance where day between '{self.begin.strftime('%Y-%m-%d')}' and '{self.tmr.strftime('%Y-%m-%d')}' group by day
        '''
        logger.info(f"report sum incoming today, sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, today_total_income, today_vip_income, today_vip_week_income,
                 today_vip_month_income) in cur.fetchall():
                data['今日收入'][day] = round(today_total_income, 2)
                data['今日VIP收入'][day] = round(today_vip_income, 2)
                data['今日月度VIP收入'][day] = round(today_vip_month_income, 2)
                data['今日周度VIP收入'][day] = round(today_vip_week_income, 2)

        return data

    def __dict_2_list(self, data: dict) -> list:

        return [data.get(x, 0) for x in self.days]
