from datetime import datetime, timedelta

from django.db import connection
from django.shortcuts import render

from Common.timeutil import TimeUtil
from Common.views import CommonView
from Lamp.settings import logger
from Server.models import OnlineUser
from Server.dao.server_dao import ServerDao
from Tongji.income_helper import IncomeHelper


class ReportSumView(CommonView):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        self.order_types = ('TRIAL', 'WEEK', 'MONTH', 'SEASON', 'YEAR')
        self.today = datetime.utcnow()
        self.begin = self.today - timedelta(days=30)
        self.tmr = self.today + timedelta(days=1)
        self.days = self.__init_days()

    def __init_days(self):
        days = []
        for i in range(30, -1, -1):
            dt = self.today - timedelta(days=i)
            days.append(dt.strftime("%Y-%m-%d"))
        return days

    def __init_days_dict(self):
        return dict([x, 0] for x in self.days)

    def __dict_2_list(self, data: dict) -> list:
        return [data.get(x, 0) for x in self.days]

    def __get_order_data(self):

        data = {}
        for t in self.order_types:
            data[t] = self.__init_days_dict()

        sql = f'''
           select DATE_FORMAT(created_at, '%%Y-%%m-%%d') as d, DATEDIFF(expired_at, created_at) as duration, count(*) 
           from Order_order
           where created_at between '%s' and '%s'
           group by d, duration
           ''' % (self.begin.strftime('%Y-%m-%d'), self.tmr.strftime('%Y-%m-%d'))
        logger.info(f"report sum, order data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, duration, count) in cur.fetchall():
                order_type = self.__get_order_type(duration)
                data[order_type][day] += count

        return data

    @staticmethod
    def __get_order_type(duration: int) -> str:
        if duration > 300:
            return 'YEAR'
        if duration > 80:
            return 'SEASON'
        if duration > 20:
            return 'MONTH'
        if duration > 5:
            return 'WEEK'
        return 'TRIAL'

    def __get_user_data(self):

        data = self.__init_days_dict()

        sql = '''
               select DATE_FORMAT(created_at, '%%Y-%%m-%%d') as d, count(*)
               from User_user
               where created_at between '%s' and '%s'
               group by d
               ''' % (self.begin.strftime('%Y-%m-%d'), self.tmr.strftime('%Y-%m-%d'))
        logger.info(f"report sum, user data,  sql: {sql}")
        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, count) in cur.fetchall():
                data[day] += count

        return data

    def __get_server_data(self):
        N = 1500
        ts_before = TimeUtil.GetNow() - timedelta(days=1)
        ts_after = TimeUtil.GetNow() + timedelta(days=1)

        data = OnlineUser.objects.filter(created_at__lte=ts_after, created_at__gte=ts_before).order_by('-id')[0: N]

        # 计算每10分钟的数据
        interval = 1  # 设置时间间隔
        ten_minute_intervals = {}
        for x in data:
            interval_start = x.created_at.replace(second=0, microsecond=0)  # 忽略秒和微秒
            interval_start -= timedelta(minutes=interval_start.minute % interval, seconds=interval_start.second)

            # 更新该时间段内的数据，取10分钟内最高的数量
            key = interval_start.strftime('%m/%d %H:%M')
            if key not in ten_minute_intervals:
                ten_minute_intervals[key] = {'ts': interval_start, 'online_cnt': 0}

            ten_minute_intervals[key]['online_cnt'] += max(int(x.vip), ten_minute_intervals[key]['online_cnt'])

        # 获取时间戳和在线人数
        ts = [interval_data['ts'].strftime('%m/%d %H:%M') for interval_data in ten_minute_intervals.values()]
        online_cnt = [interval_data['online_cnt'] for interval_data in ten_minute_intervals.values()]

        ts.reverse()
        online_cnt.reverse()

        context = {
            'ts': ts,
            'online_cnt': online_cnt,
            'server_ray_vip': ServerDao.get_all_enabled_servers_cnt(),
            'server_unavailable': ServerDao.get_all_enabled_unavailable_servers_cnt(),
        }

        return context

    def get_income_data(self):
        # 收入情况
        income_helper = IncomeHelper()
        income_total_dict = income_helper.get_income_total_data()
        income_total_user_cnt_dict = income_helper.get_income_total_data_user_cnt()
        income_today_dict = income_helper.get_income_data_today()

        income_total_data = {}
        for t in income_helper.income_total_types:
            income_total_data[t] = self.__dict_2_list(income_total_dict[t])

        income_total_user_cnt_data = {}
        for t in income_helper.income_total_user_cnt_types:
            income_total_user_cnt_data[t] = self.__dict_2_list(income_total_user_cnt_dict[t])

        income_today_data = {}
        for t in income_helper.income_today_types:
            income_today_data[t] = self.__dict_2_list(income_today_dict[t])
        logger.info(f"report sum incoming total: {income_total_dict}")

        return income_total_data, income_total_user_cnt_data, income_today_data

    def get(self, request):

        data, err_code, err_msg = self.GetDataInGet(request)
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        try:
            # 用户日活情况
            user_dict = self.__get_user_data()
            # 订单情况
            order_dict = self.__get_order_data()
            order_data = {}
            for t in self.order_types:
                order_data[t] = self.__dict_2_list(order_dict[t])

            # 服务器online人数
            server_dict = self.__get_server_data()

            context = {
                "days": self.days,
                "order_data": order_data,
                "register": self.__dict_2_list(user_dict),
            }
            context.update(server_dict)

            return render(request, 'sum.html', context)
        except Exception:
            logger.error(f"[ReportSumView] failed", exc_info=True)
            return render(request, 'sum.html', {})
