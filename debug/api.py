"""
Created on 2021/10/16

@author: jon
"""
import json
import time

import requests

from Common.util import Util


class DebugAPI:

    def __init__(self, env, case_file):

        self.base_url = 'http://localhost:8000'
        self.user_id = 6
        self.token = "xxxx"
        self.SECRETKEY = '!u2Nr&KKNZvwd&!xsAHy9(Npijed5!$0ZDcAD7Ep)IuV'
        self.DEFALUT_TOKEN = 'XXXX-TOKEN-DEFAULT-(FKTHEWALL)'

        self.case_file = case_file
        self.cases = []
        self.case_names = []

        if env.lower() == 'prd':
            self.user_id = 13
            self.base_url = 'https://api.gongbaojiding.xyz/lpapi'
            self.token = '514305e574e8f65607c96cbe3366aa53'

    def __gen_headers(self, has_user_info=True):

        header = {
            "x-userid": str(self.user_id),
            "x-token": self.token if has_user_info else self.DEFALUT_TOKEN,
            "x-timestamp": str(int(time.time() * 10 ** 6)),
            "x-device-id": '123456',
            "x-idfa": "12-34-56-78-90",
            "x-jpush-id": "jpush-id-123456"
        }

        header['x-sign'] = Util.MD5Sum(
            header['x-timestamp'] + header['x-token'] + header['x-device-id'] + self.SECRETKEY)
        print(f"headers: {header}")
        return header

    def __do_post(self, url, data):

        r = requests.post(self.base_url + url, json=data, headers=self.__gen_headers())
        print(self.base_url + url)
        print(r.content)
        return r.json()

    def __do_get(self, url, data):

        req_url = self.base_url + url
        para_str = ''
        for k in data.keys():
            para_str = para_str + '%s=%s&' % (k, data[k])

        req_url = req_url + '?' + para_str
        req_url = req_url[:-1]
        print(req_url)
        r = requests.get(req_url, headers=self.__gen_headers(True))
        print(req_url)
        print(r.content)
        return r.json()

    def __load_cases(self):

        self.cases = json.loads(open(self.case_file).read())
        for case in self.cases:
            self.case_names.append(case['name'])

    def run(self, case_names=[]):

        self.__load_cases()

        if len(case_names) == 0:
            case_names = self.case_names

        for case in self.cases:

            if case['name'] not in case_names:
                continue

            print("-" * 20, case['name'])
            print(f"RUN {case}")

            method = case.get('method', 'POST').upper()

            if method == 'POST':
                res = self.__do_post(case['url'], case.get('data', {}))

            else:
                res = self.__do_get(case['url'], case.get('data', {}))

            print(f'RESULT: {res}')


if __name__ == '__main__':
    api = DebugAPI('prd', './case.json')
    api.run([
        # 'signin',
        # 'signout',
        # 'get-user-info',
        # 'server-report',
        # 'server-country',
         'server-profile',
        # 'vip-server-profile',
        # 'get-config',
        # 'feedback',
        # 'verify-email',
        # 'reset-password',
        # 'upload-cert',
        # 'report-order',
        # 'get-version',
        # 'server-profile-v3',
    ])
