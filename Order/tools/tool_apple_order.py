import requests
from ratelimit import sleep_and_retry, limits

from Lamp import settings
from Lamp.settings import logger


class OrderAppleTool:
    @staticmethod
    def get_cert_rsp(cert: str) -> dict:
        try:
            """
            {'environment': 'Production', 'status': 21004}
            21004是Apple订阅服务器返回的错误码，它通常表示用户的付款无法完成。这可能是由于以下几种原因：
                用户的付款方式有问题：比如卡过期、卡余额不足、卡被锁定了等。
                用户尝试购买订阅但是Apple无法完成支付。这可能是由于网络问题、银行问题或者是Apple的支付系统出现问题。
                用户尝试购买内购项目，但是Apple无法验证用户的付款信息。
            """
            res, is_sandbox = OrderAppleTool.get_cert_rsp_from_apple(cert)
            logger.info(f"[OrderAppleTool] apple res：{res}, is_sandbox:{is_sandbox}")
            if 'receipt' not in res:
                logger.error(f"[OrderAppleTool] apple res：{res}, is_sandbox:{is_sandbox}")
                return {}

            receipt = res['receipt']
            bundle_id = receipt['bundle_id']

            # 检查包名是否正确
            if bundle_id != settings.APPLE_BUNDLE_ID[0]:
                logger.error(f"[OrderAppleTool] apple invalid bundle_id：{bundle_id}")
                return {}

            return receipt

        except Exception:
            logger.error(f'[OrderAppleTool] get from apple order server failed', exc_info=True)
            return {}

    @staticmethod
    @sleep_and_retry
    @limits(calls=5, period=1)
    def get_cert_rsp_from_apple(cert) -> (dict, bool):
        # 先过一次生产校验
        data = {"receipt-data": cert, "password": settings.APPLE_VERIFY_PASSWORD[0]}
        r = requests.post(settings.APPLE_VERIFY_URL, json=data)
        res = r.json()

        # 如果苹果返回21007，就要去沙箱校验
        apple_return_status = res['status']
        if apple_return_status == 21007:
            logger.info("[OrderAppleTool] apple prd env check failed，return 21007, change to sandbox env.")
            data = {"receipt-data": cert, "password": settings.APPLE_VERIFY_PASSWORD[0]}
            r = requests.post(settings.APPLE_VERIFY_URL_SANDBOX, json=data)
            res = r.json()
            return res, True
        return res, False
