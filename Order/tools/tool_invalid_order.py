import datetime
from datetime import timedelta, timezone
from typing import Union

from Common.timeutil import TimeUtil
from Lamp import settings
from Lamp.settings import logger
from Order.models import OrderSendVip
from Order.tools.tool_apple_order import OrderAppleTool


class OrderRspObject:
    def __init__(self, renew_status=0,
                 expiration_intent=0,
                 expired_at=datetime.datetime.now(timezone.utc),
                 order_status='CLOSED',
                 original_purchase_at=datetime.datetime.now(timezone.utc),
                 original_transaction_id='',
                 apple_res='',
                 is_sandbox=False):
        self.renew_status = renew_status  # 1: auto-renew, 0: not auto-renew
        # 0: bill success,
        # 1: user trial time to cancel,
        # 2: bill error,
        # 3: The customer did not agree to a recent price increase.
        # 4:The product was not available for purchase at the time of renewal.
        # 5.refund.
        # 6:other
        self.expiration_intent = expiration_intent
        self.expired_at = expired_at
        self.order_status = order_status  # "OPEN", "CLOSED"
        self.original_purchase_at = original_purchase_at
        self.original_transaction_id = original_transaction_id

        # apple_res['status']
        # 21010: 找不到或删除了该用户帐户
        # 21002: 订阅收据数据不符合格式
        # 21001: 收据未认证
        # 21004: 你提供的共享密钥和账户的共享密钥不一致
        self.apple_res = apple_res
        self.is_sandbox = is_sandbox


def create_a_forever_nice_rsp() -> OrderRspObject:
    r = OrderRspObject(renew_status=1,
                       expiration_intent=0,
                       expired_at=TimeUtil.GetNow() + timedelta(days=3),
                       order_status="OPEN",
                       original_purchase_at=TimeUtil.GetNow(),
                       original_transaction_id="",
                       apple_res="",
                       is_sandbox=True)
    return r


class OrderInvalidTool:

    @staticmethod
    def get_cert_rsp(device_id: str, cert: str) -> Union[None, OrderRspObject]:
        if device_id in settings.WHITE_USER_LIST:
            logger.info(f"[CheckOrder] white user {device_id}, run return faker expire.")
            return create_a_forever_nice_rsp()

        apple_return_status = None
        try:
            # 苹果长期订单必须要有 pending_renewal_info
            res, is_sandbox = OrderAppleTool.get_cert_rsp_from_apple(cert)
            if is_sandbox:
                logger.info(f"[CheckOrder] sandbox device_id:{device_id}, return faker expire.")
                return create_a_forever_nice_rsp()

            if 'pending_renewal_info' not in res:
                logger.warning(f'[CheckOrder] device_id：{device_id}, no pending_renewal_info in cert! '
                               f'apple res:{res}')
                return None

            # 检查包名是否正确
            receipt = res['receipt']
            bundle_id = receipt['bundle_id']
            if bundle_id != settings.APPLE_BUNDLE_ID[0]:
                logger.error(f"[CheckOrder]  device_id:{device_id}, invalid bundle_id：{bundle_id}")
                return None

            pending_renewal_info = OrderInvalidTool.get_pending_renewal_info(res, device_id)
            renew_status = pending_renewal_info.get('auto_renew_status', 0)
            expiration_intent = pending_renewal_info.get('expiration_intent', 0)

            latest_receipt_info = OrderInvalidTool.get_latest_receipt_info(device_id, res['latest_receipt_info'])
            original_transaction_id = latest_receipt_info.get('original_transaction_id', '')
            expired_ts = latest_receipt_info['expires_date_ms']
            original_purchase_ts = latest_receipt_info['original_purchase_date_ms']

            # 承诺赠送的 VIP
            send_vip = OrderSendVip.objects.filter(device_id=device_id, deleted=0).first()
            if send_vip:
                add_days = send_vip.send_days
            else:
                add_days = 0

            expire_at = TimeUtil.GetUTCDateTime(int(expired_ts))
            expire_at = TimeUtil.AddTimeDays(expire_at, add_days)
            original_purchase_at = TimeUtil.GetUTCDateTime(int(original_purchase_ts))
            order_status = 'OPEN' if TimeUtil.IsDt1BigThanNow(expire_at) else 'CLOSED'

            r = OrderRspObject(renew_status=renew_status,
                               expiration_intent=expiration_intent,
                               expired_at=expire_at,
                               order_status=order_status,
                               original_purchase_at=original_purchase_at,
                               original_transaction_id=original_transaction_id,
                               apple_res=res,
                               is_sandbox=False)
            logger.info(f'[CheckOrder] device_id: {device_id}, cert: {r}')
            return r

        except Exception:
            logger.error(f'[CheckOrder] error, device_id: {device_id}, '
                         f'apple_return_status={apple_return_status}, cert: {cert}', exc_info=True)
            return None

    @staticmethod
    def get_pending_renewal_info(res, device_id: str):
        if len(res['pending_renewal_info']) > 1:
            pending_renewal_info_list = []
            for i in res['pending_renewal_info']:
                if 'original_transaction_id' in i:
                    i['original_transaction_id'] = int(i['original_transaction_id'])
                pending_renewal_info_list.append(i)
            pending_renewal_info_list.sort(key=lambda x: x['original_transaction_id'])
            pending_renewal_info = pending_renewal_info_list[-1]
            logger.warning(f"[CheckOrder]  {device_id} multi pending_renewal_info: {pending_renewal_info_list}")
        else:
            pending_renewal_info = res['pending_renewal_info'][-1]
        return pending_renewal_info

    @staticmethod
    def get_latest_receipt_info(device_id: str, latest_receipt_info: list) -> dict:
        if len(settings.total_vip_product_ids) == 0:
            logger.error("[CONFIG ERROR] settings.total_vip_product_ids is nil")
            return {}

        # 过滤下只包含长期订单的
        _latest_receipt_info = [v for v in latest_receipt_info if
                                v['product_id'] in settings.total_vip_product_ids]

        if len(_latest_receipt_info) == 0:
            logger.error(f"[CheckOrder] user:{device_id}, has no vip order: {latest_receipt_info}")
            return {}

        latest_info = _latest_receipt_info[0]
        for info in _latest_receipt_info:
            if int(info['purchase_date_ms']) > int(latest_info['purchase_date_ms']):
                latest_info = info

        return latest_info
