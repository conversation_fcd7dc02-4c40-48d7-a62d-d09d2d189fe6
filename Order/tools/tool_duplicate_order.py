from django.conf import settings

from Lamp.settings import logger
from Order.models import OrderDuplicate
from Order.tools.tool_order import OrderTool
from User.tools.user_tool import UserTool


class OrderDuplicateTool:

    @staticmethod
    def is_duplicate(device_id: str, original_transaction_id: str, is_sandbox: bool) -> bool:
        try:

            if device_id in settings.WHITE_USER_LIST:
                logger.info(f"[OrderDuplicateTool] user: {device_id} is white, pass!!!")
                return False

            if is_sandbox:
                logger.info(f"[OrderDuplicateTool] user: {device_id} is sandbox, pass!!!")
                return False

            orders = OrderTool.get_orders_by_original_transaction_id(original_transaction_id)
            if len(orders) == 0:
                logger.info(f"[OrderDuplicateTool] user: {device_id} never has order, ok")
                return False

            exist_original_transaction_ids = [v.original_transaction_id for v in orders]
            exists_device_ids = [v.device_id for v in orders if v.device_id != device_id]
            for o in orders:
                if o.device_id == device_id:
                    logger.info(f"[OrderDuplicateTool] user: {device_id} is ok in {exists_device_ids}, ok")
                    return False
            else:
                logger.warning(f"[OrderDuplicateTool] user: {device_id} too much original_transaction_id: "
                               f"{original_transaction_id}, exist_original_transaction_ids:{exist_original_transaction_ids}")
                duplicate_device_id = exists_device_ids[0]
                od = OrderDuplicate.objects.filter(device_id=device_id,
                                                   original_transaction_id=original_transaction_id).first()
                if not od:
                    logger.warning(f"[OrderDuplicateTool] user: {device_id} duplicate: {duplicate_device_id} save")
                    duplicate_user = UserTool.get_user_by_device_id_with_deleted(duplicate_device_id)
                    if duplicate_user:
                        od = OrderDuplicate(device_id=device_id,
                                            original_transaction_id=original_transaction_id,
                                            duplicate_device_id=duplicate_device_id,
                                            duplicate_email='')
                        od.save()
                    else:
                        logger.warning(f"[OrderDuplicateTool] user: {device_id} not exist?")
                else:
                    logger.warning(
                        f"[OrderDuplicateTool] user: {device_id} duplicate: {duplicate_device_id} already saved")

                logger.warning(
                    f"[OrderDuplicateTool] user: {device_id} original_transaction_id: {original_transaction_id}, "
                    f"exists_original_transaction_ids：{exist_original_transaction_ids}, duplicate!!!")
                return True
        except Exception:
            logger.error(
                f"[OrderDuplicateTool] user: {device_id} original_transaction_id: {original_transaction_id} "
                f"failed", exc_info=True)
            return False
