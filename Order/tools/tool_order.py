import datetime
from typing import Union

from django.db.models import F

from Common.err import ErrInfo
from Common.timeutil import TimeUtil
from Lamp import settings
from Lamp.settings import logger
from Order.models import Order, OrderDuplicate


class OrderTool:

    @staticmethod
    def get_user_order_without_condition(device_id: str) -> Union[Order, None]:
        order = Order.objects.filter(device_id=device_id).first()
        if order:
            return order
        else:
            return None

    @staticmethod
    def is_user_vip_expire(device_id: str) -> ErrInfo:
        if device_id in settings.WHITE_USER_LIST:
            logger.info(f"[OrderTool.is_user_vip_expire] user: {device_id} is white user, yes vip.")
            return ErrInfo.SUCCESS

        # 这里不允许closed订单出现，不能豁免
        order = OrderTool.get_user_order(device_id)
        if not order:
            logger.info(f"[OrderTool.is_user_vip_expire] user: {device_id} has no order, not vip.")
            return ErrInfo.JUMP_VIP_VIEW

        assert isinstance(order, Order)
        if TimeUtil.GetDiffMinutesWithNow(order.expired_at) < 1:
            logger.info(f"[OrderTool.is_user_vip_expire] used: {device_id}, "
                        f"order expire at {order.expired_at}, now is expired, is not vip.")
            return ErrInfo.JUMP_VIP_VIEW

        return ErrInfo.SUCCESS

    @staticmethod
    def is_user_trail_vip(device_id: str) -> bool:
        # 至少是一个合法订单
        if OrderTool.is_user_vip_expire(device_id) != ErrInfo.SUCCESS:
            return False

        # 再来判断是不是试用期
        return Order.objects.filter(device_id=device_id,
                                    expired_at__lte=F('created_at') + datetime.timedelta(days=4)).exists()

    @staticmethod
    def get_order_by_cert(cert: str) -> Union[Order, None]:
        return Order.objects.filter(certificate=cert).first()

    @staticmethod
    def get_order_ignore_status(device_id: str) -> Union[Order, None]:
        order = Order.objects.filter(device_id=device_id).first()
        if order:
            return order
        else:
            return None

    @staticmethod
    def get_user_order(device_id: str) -> Union[Order, None]:
        if not device_id:
            logger.error(f"[OrderTool.get_user_order] device_id is null")
            return None

        order = Order.objects.filter(device_id=device_id, order_status='OPEN', valid=1).first()
        if order:
            return order
        else:
            return None

    @staticmethod
    def get_orders_by_original_transaction_id(original_transaction_id: str) -> list:
        orders = Order.objects.filter(original_transaction_id=original_transaction_id).all()
        return orders

    @staticmethod
    def get_user_expire(device_id: str) -> Union[datetime.datetime, None]:
        order = OrderTool.get_user_order(device_id)
        if order:
            return order.expired_at
        else:
            return None

    @staticmethod
    def get_user_vip_days(device_id: str) -> int:
        order = OrderTool.get_user_order(device_id)
        if not order:
            logger.warning(f"[OrderTool.get_user_vip_days] device_id:{device_id} order not exists or expire")
            return 0

        return TimeUtil.GetDiffDays(TimeUtil.GetNow(), order.created_at)

    @staticmethod
    def get_user_left_vip_days(device_id: str) -> int:
        order = OrderTool.get_user_order(device_id)
        if not order:
            logger.warning(f"[OrderTool.get_user_left_vip_days] device_id:{device_id} order not exists or expired")
            return 0

        left_vip_days = TimeUtil.GetDiffDays(order.expired_at, TimeUtil.GetNow())
        if left_vip_days < 0:
            logger.error(f"[OrderTool.get_user_left_vip_days] device_id:{device_id} left_vip_days:{left_vip_days} < 0")
            return 0

        return left_vip_days

    @staticmethod
    def get_duplicate_info_list(device_id: str) -> list:
        records = OrderDuplicate.objects.filter(device_id=device_id).all()
        res = []
        for r in records:
            res.append({
                "original_transaction_id": r.original_transaction_id,
                "duplicate_device_id": r.duplicate_device_id,
                "duplicate_email": r.duplicate_email,
                "created_at": TimeUtil.GetBeijingTimeStr(r.created_at),
            })
        return res
