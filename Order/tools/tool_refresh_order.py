from datetime import datetime

from django.db import transaction

from Common.err import ErrInfo
from Common.timeutil import TimeUtil
from Common.util import Util
from Lamp.settings import logger
from Order.models import OrderDuplicate, Order
from Order.tools.tool_invalid_order import OrderInvalidTool
from Order.tools.tool_order import OrderTool
from User.models import User


class RefreshOrderTool:
    @staticmethod
    def recover_order_subscription(device_id: str) -> (bool, str):
        try:
            od = OrderDuplicate.objects.filter(device_id=device_id).first()
            if not od:
                logger.warning(
                    f"[RefreshOrderTool.recover_order_subscription] user:{device_id} has no duplicate user_id")
                return False, "can't find duplicate user_id to recover"

            if OrderTool.is_user_vip_expire(device_id) == ErrInfo.SUCCESS:
                logger.warning(f"[RefreshOrderTool.recover_order_subscription] user:{device_id} is already a vip")
                return False, "user is already a vip!"

            if OrderTool.get_order_ignore_status(device_id):
                logger.warning(f"[RefreshOrderTool.recover_order_subscription] user:{device_id} is "
                               f"already has a subscription")
                return False, "user already has a subscription!"

            old_device_id = od.duplicate_device_id
            new_device_id = device_id

            with transaction.atomic():
                Order.objects.filter(device_id=old_device_id).update(device_id=new_device_id)
                RefreshOrderTool.refresh_user_vip(new_device_id)

            return True, "success"
        except Exception:
            logger.error(f"[RefreshOrderTool.recover_order_subscription] user:{device_id} recover failed",
                         exc_info=True)
            return False, "recover order subscription failed"

    @staticmethod
    def refresh_user_vip(device_id: str):
        order = OrderTool.get_user_order_without_condition(device_id)
        if not order:
            od = OrderDuplicate.objects.filter(device_id=device_id).first()
            if od:
                logger.warning(f"[CheckOrderStatus] userid: {device_id}, order not exists!")
                return {"err": f"duplicate order",
                        "duplicate_device_id": od.duplicate_device_id,
                        "duplicate_email": od.duplicate_email,
                        "话术": f"Hello, may I ask if this account belongs to you? Your subscription belongs to this "
                                f"account：{Util.mask_emails_correctly(od.duplicate_email)}"
                        }
            logger.warning(f"[CheckOrderStatus] userid: {device_id}, order not exists!")
            return {"err": "invalid order, not exist"}

        cert_rsp = OrderInvalidTool.get_cert_rsp(order.device_id, order.certificate)
        if not cert_rsp:
            return {"err": "invalid order, no expired_at"}

        # 订单状态对不上、过期时间对不上、取消理由对不上则重新刷新
        ret = {
            "refresh": 0,
            "device_id": device_id,
        }

        if order.expiration_intent != int(cert_rsp.expiration_intent) \
                or order.order_status != cert_rsp.order_status \
                or abs(TimeUtil.GetDiffMinutes(order.expired_at, cert_rsp.expired_at)) >= 10:
            ret["refresh"] = 1
            ret["before_original_transaction_id"] = order.original_transaction_id
            ret["before_renew_status"] = order.renew_status
            ret["before_expiration_intent"] = order.expiration_intent
            ret["before_expired_at"] = order.expired_at
            ret["before_order_status"] = order.order_status

            order.original_transaction_id = cert_rsp.original_transaction_id
            order.renew_status = cert_rsp.renew_status
            order.expiration_intent = cert_rsp.expiration_intent
            order.expired_at = cert_rsp.expired_at
            order.order_status = cert_rsp.order_status
            order.save()

        # 更新用户的expire_at
        RefreshOrderTool.update_user_all_expire(order.device_id, order.expired_at)

        ret.update({
            "original_transaction_id": cert_rsp.original_transaction_id,
            "renew_status": int(cert_rsp.renew_status),
            "expired_at": cert_rsp.expired_at,
            "order_status": cert_rsp.order_status,
            "expiration_intent": int(cert_rsp.expiration_intent),
            "is_order_valid": order.valid,
        })
        return ret

    @staticmethod
    def update_user_all_expire(device_id: str, expired_at: datetime):
        """
        更新所有生命周期的过期时间： order.order.expired_at + user.expired_at +  number_used.expired_at
        :param device_id: 用户ID
        :param expired_at: 订单实际的过期时间
        :return:
        """
        try:
            if not expired_at:
                logger.error(f"[UserVipTool.update_user_all_expire] update {device_id} expire: {expired_at} is empty")
                return

            with transaction.atomic():
                logger.info(f"[UserVipTool.update_user_all_expire] update user {device_id} expire: {expired_at}")
                User.objects.filter(device_id=device_id).update(expired_at=expired_at)

        except Exception:
            logger.error(f"[RefreshOrderTool.update_user_all_expire] {device_id} {expired_at} failed", exc_info=True)
