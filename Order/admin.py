from django.contrib import admin

from Order.models import Order, OrderSendVip, OrderDuplicate


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'device_id', 'renew_status', 'expiration_intent', 'expired_at',
                    'original_transaction_id', 'valid', 'order_status', 'created_at', 'updated_at',)
    readonly_fields = ('device_id', 'original_transaction_id', 'cert_md5', 'certificate',)
    exclude = ('certificate', 'appid',)
    list_per_page = 50  # 设置每页显示 50 条记录

    class Media:
        js = ('admin/js/custom_order_record_admin.js',)  # 引入自定义的 JavaScript 代码
        css = {
            'all': ('admin/css/custom_order_record_admin.css',)  # 引入自定义的 CSS 文件
        }

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        device_id = request.GET.get('device_id', '').strip()
        original_transaction_id = request.GET.get('original_transaction_id', '').strip()
        cert_md5 = request.GET.get('cert_md5', '').strip()

        if device_id:
            queryset = queryset.filter(device_id=device_id)
        if original_transaction_id:
            queryset = queryset.filter(original_transaction_id=original_transaction_id)
        if cert_md5:
            queryset = queryset.filter(cert_md5=cert_md5)
        return queryset


@admin.register(OrderSendVip)
class OrderSendVipAdmin(admin.ModelAdmin):
    list_display = ('id', 'device_id', 'send_days', 'deleted', 'created_at', 'updated_at',)
    search_fields = ('device_id',)
    list_filter = ('created_at', 'send_days',)
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(OrderDuplicate)
class OrderDuplicateAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'device_id', 'original_transaction_id', 'duplicate_device_id', 'duplicate_email', 'created_at',
        'updated_at',)
    search_fields = ('device_id', 'original_transaction_id', 'duplicate_device_id', 'duplicate_email',)
    list_per_page = 50  # 设置每页显示 50 条记录
