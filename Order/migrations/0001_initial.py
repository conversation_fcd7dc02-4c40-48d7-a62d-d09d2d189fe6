# Generated by Django 3.2.5 on 2022-10-08 02:39

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.IntegerField()),
                ('renew_status', models.IntegerField(blank=True)),
                ('expiration_intent', models.IntegerField(blank=True, default=0)),
                ('expire_at', models.DateTimeField(blank=True)),
                ('valid', models.IntegerField(default=1)),
                ('order_status', models.CharField(blank=True, default='OPEN', max_length=20)),
                ('certificate', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
