"""
Created on 2021/10/30

@author: jon
"""
import threading
import time
from datetime import timedelta

from Common.err import ErrInfo
from Common.timeutil import TimeUtil
from Common.util import Util
from Common.views import CommonView
from Lamp.settings import logger
from Order.apple import AppleVerifyUtil
from Order.common import OrderCommon
from Order.models import Order
from Order.tools.tool_duplicate_order import OrderDuplicateTool
from Order.tools.tool_order import OrderTool
from Server.common import ServerCommon


class PostCertificate(CommonView):

    def __save_order_to_db(self, device_id: str, appid: int, cert_md5: str, cert: str):
        """
        保存订单到数据库
        :param device_id:
        :param appid:
        :param cert_md5:
        :param cert:
        :return:
        """
        order = Order.objects.filter(device_id=device_id)
        if order.count() == 0:
            order = Order(
                device_id=device_id,
                certificate=cert,
                cert_md5=cert_md5,
                appid=appid,
                order_status='OPEN',
                valid=1,
            )
            order.save()
        else:
            order = order[0]
            order.cert_md5 = cert_md5
            order.certificate = cert
            order.appid = appid
            order.order_status = 'OPEN'
            order.valid = 1
            order.save()
        return order

    ## /order/cert/
    @CommonView.VerifySign
    def post(self, request):

        post_data, err_code, err_msg = self.ReadPostJson(request, check_list=['cert', ])
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        try:
            # 一个订单只能过来成功校验一次，如果cert落库后就不能继续校验了
            header = self.GetHeaderInRequest(request)
            device_id = header['device_id']
            appid = header['appid']
            cert = post_data['cert'].replace('\n', '').replace('\r', '')
            cert_md5 = Util.MD5Sum(cert)
            logger.info(f'[PostCertificate] device_id={device_id}, appid:{appid}, cert_md5:{cert_md5}, cert:{cert}')

            # 去苹果校验订单
            renew_status, expiration_intent, expired_at, order_status, original_purchase_at, apple_ret, \
                original_transaction_id, is_sandbox = OrderCommon.GetCertStatusAndExpireDate(device_id, cert, appid)
            if expired_at is None:
                logger.info(f"[PostCertificate] device_id:{device_id} 凭证没有过期时间！")
                return self.ReturnError(ErrInfo.INVALID_CERT)

            if not is_sandbox:
                # 判断订单重复
                if OrderDuplicateTool.is_duplicate(device_id, original_transaction_id, is_sandbox):
                    logger.warning(
                        f"[PostCertificate] user:{device_id}, original_transaction_id:{original_transaction_id} duplicate!")
                    # 先把之前的订阅下线
                    old_orders = OrderTool.get_orders_by_original_transaction_id(original_transaction_id)
                    for old_order in old_orders:
                        logger.warning(f"[PostCertificate] user:{device_id}, "
                                       f"original_transaction_id:{original_transaction_id} set valid=0, "
                                       f"old_order_id:{old_order.id}, old_order_device_id: {old_order.device_id} ")
                        old_order.valid = 0
                        old_order.save()

            # 把订单存入数据库
            order = self.__save_order_to_db(device_id, appid, cert_md5, cert)

            # 更新订单
            order.renew_status = renew_status
            order.expiration_intent = expiration_intent
            order.expired_at = expired_at
            order.valid = 1
            order.order_status = order_status
            order.original_transaction_id = original_transaction_id
            order.is_sandbox = 1 if is_sandbox else 0
            order.save()

            ret = {"renew_status": renew_status, "expired_at": expired_at}
            logger.info(f"[check order cert] success：{ret}")
            return self.ReturnSuccess(ret)
        except Exception as e:
            if "Duplicate entry" in str(e):
                logger.warning(f"[check order cert] duplicate entry: {e}")
                return self.ReturnSuccess()

            logger.error(f"[check order cert] failed, post_data: {post_data}", exc_info=True)
            return self.ReturnError(ErrInfo.JUMP_VIP_VIEW)


# filter updated_at > days_ago, default is yesterday
class UpdateOrderStatus(CommonView):

    @staticmethod
    def do_update_order_status(days_ago: int):

        expired_after = TimeUtil.GetNow() + timedelta(days=days_ago)
        expired_before = TimeUtil.GetNow() - timedelta(days=days_ago)

        t1 = time.time()
        logger.info(f"[UpdateOrderStatus] days: {days_ago}, begin: {expired_before}, end: {expired_after}")

        # 遍历所有订单 根据创建 + 过期时间2个维度
        orders1 = Order.objects.filter(created_at__lte=expired_after, created_at__gte=expired_before,
                                       order_status='OPEN').all()
        orders2 = Order.objects.filter(expired_at__lte=expired_after, expired_at__gte=expired_before,
                                       order_status='OPEN').all()

        # 合并2个订单
        orders1_ids = [v.id for v in orders1]
        orders = [v for v in orders1] + [v for v in orders2 if v.id not in orders1_ids]
        logger.info(f"[UpdateOrderStatus] step 1: check OPEN STATUS, creted_at and expired_at "
                    f"between: {expired_before}, {expired_after} order size = {len(orders)}")

        for index, order in enumerate(orders):
            device_id = order.device_id
            logger.info(f"[UpdateOrderStatus] index:{index}, device_id:{device_id}, order_id: {order.id}")

            renew_status, expiration_intent, expired_at, order_status, original_purchase_at, apple_ret, original_transaction_id, is_sandbox = \
                OrderCommon.GetCertStatusAndExpireDate(device_id, order.certificate, order.appid)
            logger.info(
                f"[UpdateOrderStatus] device_id:{device_id}, orderid: {order.id}, renew_status: {renew_status}, "
                f"expiration_intent: {expiration_intent}, expired_at: {expired_at},"
                f"original_transaction_id: {original_transaction_id}, is_sandbox:{is_sandbox}")
            if expired_at is not None:
                order.renew_status = renew_status
                order.expiration_intent = expiration_intent
                order.expired_at = expired_at
                order.order_status = order_status
                order.original_transaction_id = original_transaction_id
                order.is_sandbox = 1 if is_sandbox else 0

                # 过期则下线
                if not TimeUtil.IsDt1BigThanNow(expired_at):
                    ServerCommon.delete_client_user(device_id)
            else:
                logger.error(f"[UpdateOrderStatus] device_id:{device_id}, order_id = {order.id} has no expired_at!")
                # 奇怪订单则下线
                ServerCommon.delete_client_user(device_id)

                # 维护一些奇奇怪怪的订单的expiration_intent
                if expiration_intent is not None:
                    logger.error(f"[UpdateOrderStatus] device_id:{device_id}, order_id = {order.id}, "
                                 f"expiration_intent: {expiration_intent}")
                    order.expiration_intent = expiration_intent
                    order.original_transaction_id = original_transaction_id
            order.save()

        t2 = time.time()
        logger.info(f"[UpdateOrderStatus] finished check orders, cost: {int((t2 - t1) * 1000)} ms")

    ## /order/status/update/?days=3
    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, check_list=('token',))
        if err_code != 0:
            logger.info(f"[UpdateOrderStatus] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(err_code, err_msg)

        if data['token'] != 'watch-pig-v003':
            logger.error(f"[UpdateOrderStatus] token: {data['token']} is invalid!")
            return self.ReturnError(-1, "token is invalid")

        days_ago = int(data.get('days', 7))
        t = threading.Thread(target=UpdateOrderStatus.do_update_order_status, args=(days_ago,))
        t.start()

        return self.ReturnSuccess()


# filter updated_at > days_ago, default is yesterday
class CheckOrderStatus(CommonView):

    ## /order/check/?device_id=123
    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, ("device_id", "token",))
        if err_code != 0:
            logger.info(f"[CheckOrderStatus] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(err_code, err_msg)

        data, _, _ = self.GetDataInGet(request)
        device_id = data.get('device_id', '')
        token = data.get('token')
        if token != 'watch-cat-hello':
            logger.error(f"[CheckOrderStatus] device_id: {device_id}, token: {token} is invalid!")
            return self.ReturnError(-1, "token is invalid")

        order = Order.objects.filter(device_id=device_id).first()
        if order.valid == 0:
            logger.error(f"[CheckOrderStatus] device_id: {device_id}, order is invalid!")
            return self.ReturnError(-1, "order is invalid")

        renew_status, expiration_intent, expired_at, order_status, original_purchase_at, apple_ret, original_transaction_id, is_sandbox = \
            OrderCommon.GetCertStatusAndExpireDate(order.device_id, order.certificate, order.appid)
        logger.info(f"[CheckOrderStatus] device_id:{device_id}, order_id: {order.id}, renew_status: {renew_status}, "
                    f"expiration_intent: {expiration_intent}, expired_at: {expired_at}, order_status: {order_status}, "
                    f"original_transaction_id={original_transaction_id}, is_sandbox: {is_sandbox}")

        if not expired_at:
            logger.warning(f"[CheckOrderStatus] device_id: {device_id}, order has no expire, is invalid!")
            if expiration_intent == 6:
                order.expiration_intent = expiration_intent
                order.save()
            return self.ReturnError(-1, "order is invalid")

        # 订单状态对不上、过期时间对不上、取消理由对不上则重新刷新
        ret = {
            "refresh": 0,
            "device_id": device_id,
        }

        if order.expiration_intent != int(expiration_intent) \
                or order.order_status != order_status \
                or abs(TimeUtil.GetDiffMinutes(order.expired_at, expired_at)) >= 10:
            ret["refresh"] = 1
            ret["before_original_transaction_id"] = order.original_transaction_id
            ret["before_renew_status"] = order.renew_status
            ret["before_expiration_intent"] = order.expiration_intent
            ret["before_expired_at"] = order.expired_at
            ret["before_order_status"] = order.order_status

            order.original_transaction_id = original_transaction_id
            order.renew_status = renew_status
            order.expiration_intent = expiration_intent
            order.expired_at = expired_at
            order.order_status = order_status
            order.is_sandbox = 1 if is_sandbox else 0
            order.save()

        # 沙盒的订阅不用管
        if is_sandbox:
            ret["refresh"] = 0

        # 如果订阅过期则下线
        if order_status == "CLOSED":
            logger.info(f"[CheckOrderStatus] notify all server to delete user client, device_id:{device_id}")
            ServerCommon.delete_client_user(device_id)

        ret.update({
            "original_transaction_id": original_transaction_id,
            "renew_status": int(renew_status),
            "expired_at": expired_at,
            "order_status": order_status,
            "expiration_intent": int(expiration_intent),
        })
        return self.ReturnSuccess(data=ret)


class BatchCheckOrderStatus(CommonView):

    def post(self, request):
        data, err_code, err_msg = self.ReadPostJson(request, ('device_ids', 'token',))
        if err_code != 0:
            logger.error(f"[BatchCheckOrderStatus] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(err_code, err_msg)

        device_ids = data.get('device_ids', [])
        if data['token'] != 'watch-cat-hello':
            logger.error(f"[BatchCheckOrderStatus] device_ids: {device_ids}, token: {data['token']} is invalid!")
            return self.ReturnError(-1, "token is invalid")

        res = []
        need_offline_device_ids = []
        for device_id in device_ids:
            order = Order.objects.filter(device_id=device_id).first()
            if order.valid == 0:
                logger.error(f"[BatchCheckOrderStatus] device_id: {device_id}, order is invalid!")
                res.append({"err_code": 1, "err_msg": f"{device_id}: order is invalid"})
                continue

            # 之前的订单状态
            before_order_status = order.order_status

            renew_status, expiration_intent, expired_at, order_status, original_purchase_at, apple_ret, original_transaction_id, is_sandbox = \
                OrderCommon.GetCertStatusAndExpireDate(order.device_id, order.certificate, order.appid)
            logger.info(
                f"[BatchCheckOrderStatus] device_id:{device_id}, order_id: {order.id}, renew_status: {renew_status}, "
                f"expiration_intent: {expiration_intent}, expired_at: {expired_at}, order_status: {order_status}, "
                f"original_transaction_id={original_transaction_id}, is_sandbox: {is_sandbox}")

            if not expired_at:
                logger.warning(f"[BatchCheckOrderStatus] device_id: {device_id}, order has no expire, is invalid!")
                if expiration_intent == 6:
                    order.expiration_intent = expiration_intent
                    order.save()
                res.append({"err_code": 1, "err_msg": f"{device_id}: order is invalid"})
                continue

            # 订单状态对不上、过期时间对不上、取消理由对不上则重新刷新
            ret = {
                "refresh": 0,
                "device_id": device_id,
            }

            if order.expiration_intent != int(expiration_intent) \
                    or order.order_status != order_status \
                    or abs(TimeUtil.GetDiffMinutes(order.expired_at, expired_at)) >= 10:
                ret["refresh"] = 1
                ret["before_original_transaction_id"] = order.original_transaction_id
                ret["before_renew_status"] = order.renew_status
                ret["before_expiration_intent"] = order.expiration_intent
                ret["before_expired_at"] = order.expired_at
                ret["before_order_status"] = order.order_status

                order.original_transaction_id = original_transaction_id
                order.renew_status = renew_status
                order.expiration_intent = expiration_intent
                order.expired_at = expired_at
                order.order_status = order_status
                order.is_sandbox = 1 if is_sandbox else 0
                order.save()

            # 沙盒的订阅不用管
            if is_sandbox:
                ret["refresh"] = 0

            # 如果订阅过期则下线
            if before_order_status != "CLOSED" and order_status == "CLOSED":
                logger.info(f"[BatchCheckOrderStatus] notify all server to delete user client, device_id:{device_id}")
                need_offline_device_ids.append(device_id)

            ret.update({
                "original_transaction_id": original_transaction_id,
                "renew_status": int(renew_status),
                "expired_at": expired_at,
                "order_status": order_status,
                "expiration_intent": int(expiration_intent),
            })
            res.append(ret)

        # 通知下线
        if need_offline_device_ids:
            ServerCommon.batch_delete_client_user(need_offline_device_ids)

        logger.info(
            f"[BatchCheckOrderStatus] done, all size:{len(res)}, need_offline_device_ids:{len(need_offline_device_ids)}")
        return self.ReturnSuccess(data=res)


# 单纯看看苹果的验证返回
class AppleVerify(CommonView):

    ## /order/appleVerify/?device_id=123&token=xxx
    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, ("device_id", "token",))
        if err_code != 0:
            logger.info(f"[AppleVerify] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(err_code, err_msg)

        data, _, _ = self.GetDataInGet(request)
        device_id = data.get('device_id', '')
        token = data.get('token')
        if token != 'fatpo-hello-2':
            return self.ReturnError(-1, "token is invalid")

        order = Order.objects.filter(device_id=device_id).first()
        if not order:
            return self.ReturnError(-1, "order is not exist")
        res = AppleVerifyUtil.GetCertRspFromApple(order.certificate, 0)
        return self.ReturnSuccess(data=res)
