"""
Created on 2021/10/30

@author: jon
"""

from django.db import models


class OrderManager(models.Manager):
    pass


class Order(models.Model):
    device_id = models.CharField(max_length=128)

    renew_status = models.IntegerField(blank=True, default=0)  # 1: auto-renew, 0: not auto-renew
    expiration_intent = models.IntegerField(blank=True,
                                            default=0)  # 0: bill success, 1: user cancel, 2: bill error, 3: other

    expired_at = models.DateTimeField(blank=True, null=True)

    valid = models.IntegerField(default=1)  # 0: invalid, 1: valid
    order_status = models.CharField(default='OPEN', blank=True, max_length=20)  ## OPEN, CLOSED,

    original_transaction_id = models.CharField(default='', blank=True, max_length=128)
    cert_md5 = models.CharField(default='', blank=True, max_length=64)
    certificate = models.TextField()

    appid = models.IntegerField(default=0)
    is_sandbox = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = OrderManager()


class OrderSendVip(models.Model):
    device_id = models.CharField(max_length=64)
    send_days = models.IntegerField(default=0)
    deleted = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class OrderDuplicate(models.Model):
    device_id = models.CharField(max_length=64)
    original_transaction_id = models.CharField(default='', blank=True, max_length=128)
    duplicate_device_id = models.CharField(max_length=64)
    duplicate_email = models.CharField(default='', blank=True, max_length=64)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class BlackOrderRecord(models.Model):
    device_id = models.CharField(max_length=64)
    original_transaction_id = models.CharField(blank=False, max_length=128)
    cert_md5 = models.CharField(blank=False, max_length=128)
    ban_days = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()
