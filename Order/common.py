"""
Created on 2021/10/30

@author: jon
"""
import datetime
from datetime import timedelta

from django.conf import settings

from Common import timeutil
from Common.timeutil import TimeUtil
from Lamp.settings import logger
from Order.apple import AppleVerifyUtil
from Order.models import Order


class OrderCommon:

    @staticmethod
    def is_expired(device_id: str) -> (bool, datetime.datetime):

        if device_id in settings.WHITE_USER_LIST:
            return False, TimeUtil.GetNow() + timedelta(days=30)
        # todo: delete it
        else:
            return False, TimeUtil.GetNow() + timedelta(days=3)

        order = Order.objects.filter(device_id=device_id).first()
        if not order or order.order_status != 'OPEN' or \
                timeutil.TimeUtil.GetDiffMinutesWithNow(order.expired_at) < 0:
            is_expired = True
            expired_at = None
        else:
            is_expired = False
            expired_at = order.expired_at.strftime("%Y-%m-%d %H:%M:00 UTC")
        return is_expired, expired_at

    @staticmethod
    def GetCertStatusAndExpireDate(device_id: str, cert: str, appid: int):
        """
        返回：
          # renew_status, expiration_intent, expire_at, order_status, original_purchase_at, res, original_transaction_id, is_sandbox

        :param device_id:
        :param cert:
        :param appid:
        :return:
        """
        apple_return_status = None
        try:
            if device_id in settings.WHITE_USER_LIST:
                logger.info(
                    f"[GetCertStatusAndExpireDate] white user list: {settings.WHITE_USER_LIST}, user:{device_id},"
                    f" run return faker expire.")
                return 1, 0, TimeUtil.GetNow() + timedelta(days=30), "OPEN", TimeUtil.GetNow(), None, None, True

            # 苹果长期订单必须要有 pending_renewal_info
            res, is_sandbox = AppleVerifyUtil.GetCertRspFromApple(cert, appid)
            if is_sandbox:
                logger.info(f"[GetCertStatusAndExpireDate] sandbox device_id: {device_id},"
                            f" run return faker expire.")
                return 1, 0, TimeUtil.GetNow() + timedelta(days=30), "OPEN", TimeUtil.GetNow(), None, None, True

            if 'pending_renewal_info' not in res:
                logger.warning(f'[GetCertStatusAndExpireDate] device_id：{device_id} check order error, '
                               f'no pending_renewal_info in cert! apple res:{res}')
                # 21010: 找不到或删除了该用户帐户
                if res['status'] in [21010]:
                    return None, 6, None, None, None, None, None, False
                else:
                    return None, None, None, None, None, None, None, False

            # 检查包名是否正确
            receipt = res['receipt']
            bundle_id = receipt['bundle_id']
            if bundle_id != settings.APPLE_BUNDLE_ID[appid]:
                logger.error(f"[GetCertStatusAndExpireDate] 订单校验失败，包名不正确：{bundle_id}")
                return None, None, None, None, None, None, None, False

            if len(res['pending_renewal_info']) > 1:
                pending_renewal_info_list = []
                for i in res['pending_renewal_info']:
                    if 'original_transaction_id' in i:
                        i['original_transaction_id'] = int(i['original_transaction_id'])
                    pending_renewal_info_list.append(i)
                pending_renewal_info_list.sort(key=lambda x: x['original_transaction_id'])
                pending_renewal_info = pending_renewal_info_list[-1]
                logger.error(
                    f"[GetCertStatusAndExpireDate] device_id: {device_id} 订单有多个 pending_renewal_info: {pending_renewal_info_list}")
            else:
                pending_renewal_info = res['pending_renewal_info'][-1]

            renew_status = pending_renewal_info['auto_renew_status']
            expiration_intent = pending_renewal_info.get('expiration_intent', 0)

            latest_receipt_info = OrderCommon.get_latest_receipt_info(res['latest_receipt_info'])

            original_transaction_id = latest_receipt_info.get('original_transaction_id', '')
            expired_ts = latest_receipt_info['expires_date_ms']
            original_purchase_ts = latest_receipt_info['original_purchase_date_ms']

            expire_at = TimeUtil.GetUTCDateTime(int(expired_ts))
            original_purchase_at = TimeUtil.GetUTCDateTime(int(original_purchase_ts))

            order_status = 'OPEN' if TimeUtil.IsDt1BigThanNow(expire_at) else 'CLOSED'

            # 退款如实返回，过期时间应该为取消订单的时间
            if 'cancellation_date' in latest_receipt_info:
                logger.warning(f'[GetCertStatusAndExpireDate] device_id: {device_id} '
                               f'expire:{latest_receipt_info.get("expires_date", "")} has cancelled order at : '
                               f'{latest_receipt_info.get("cancellation_date", "")}')
                order_status = 'CLOSED'
                expire_at = TimeUtil.GetUTCDateTime(int(latest_receipt_info["cancellation_date_ms"]))
                expiration_intent = pending_renewal_info.get('expiration_intent', 5)
                return renew_status, expiration_intent, expire_at, order_status, original_purchase_at, res, original_transaction_id, False
            else:
                logger.info(
                    f'[GetCertStatusAndExpireDate] device_id: {device_id} get order success, renew_status={renew_status}, '
                    f'expiration_intent={expiration_intent}, expired_ts={expired_ts}, order_status={order_status}, '
                    f'original_purchase_ts={original_purchase_ts}')
                return renew_status, expiration_intent, expire_at, order_status, original_purchase_at, res, original_transaction_id, False

        except Exception:
            logger.error(
                f'[GetCertStatusAndExpireDate] error, device_id: {device_id}, apple_return_status={apple_return_status}',
                exc_info=True)
            return None, None, None, None, None, None, None, False

    @staticmethod
    def get_latest_receipt_info(latest_receipt_info: list) -> dict:
        # 过滤下只包含长期订单的
        _latest_receipt_info = [v for v in latest_receipt_info if
                                ('month' in v['product_id']
                                 or 'quarter' in v['product_id']
                                 or 'week' in v['product_id']
                                 or 'year' in v['product_id'])]
        if len(_latest_receipt_info) == 0:
            logger.error(f"OrderCommon.get_latest_receipt_info has no vip order: {latest_receipt_info}")
            return {}

        latest_info = _latest_receipt_info[0]
        for info in _latest_receipt_info:
            if int(info['purchase_date_ms']) > int(latest_info['purchase_date_ms']):
                latest_info = info

        return latest_info
