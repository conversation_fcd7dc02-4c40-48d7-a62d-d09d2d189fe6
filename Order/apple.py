import json
from typing import Tuple

import requests
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry

from Lamp import settings
from Lamp.settings import logger


class AppleVerifyUtil:
    @staticmethod
    def _get_session_with_retries() -> requests.Session:
        """创建带重试机制的会话（修复urllib3版本兼容性）"""
        session = requests.Session()
        retry_args = dict(
            total=3,
            backoff_factor=0.5,
            status_forcelist=[500, 502, 503, 504]
        )

        # 检测urllib3版本兼容性
        try:
            # 尝试使用新版参数（urllib3 >= 1.26）
            Retry(allowed_methods=["POST"], **retry_args)  # 预检查参数兼容性
            retry_args["allowed_methods"] = ["POST"]
        except TypeError:
            # 旧版使用method_whitelist（urllib3 < 1.26）
            retry_args.pop("allowed_methods", None)  # 确保移除可能存在的旧参数
            retry_args["method_whitelist"] = ["POST"]

        retries = Retry(**retry_args)
        adapter = HTTPAdapter(max_retries=retries)
        session.mount('https://', adapter)
        session.mount('http://', adapter)
        return session

    @staticmethod
    def GetCertRspFromApple(cert: str, appid: int) -> Tuple[dict, bool]:
        """
        获取苹果支付验证结果（已修复urllib3兼容性）
        :param cert: 支付凭证
        :param appid: 应用ID
        :return: (结果字典, 是否尝试了沙箱环境)
        """
        session = AppleVerifyUtil._get_session_with_retries()
        timeout = 30  # 秒

        data = {"receipt-data": cert, "password": settings.APPLE_VERIFY_PASSWORD.get(appid, "")}
        sandbox_tried = False

        # 生产环境验证
        try:
            logger.info("[Order.apple.get_cert_rsp_from_apple] 开始苹果生产环境验证...")
            response = session.post(settings.APPLE_VERIFY_URL, json=data, timeout=timeout)
            response.raise_for_status()
            res = response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"[Order.apple.get_cert_rsp_from_apple] 生产环境请求异常: {str(e)}")
            return {"status": -1, "error": str(e)}, sandbox_tried
        except json.JSONDecodeError as e:
            logger.error(f"[Order.apple.get_cert_rsp_from_apple] 生产环境响应解析失败: {str(e)}")
            return {"status": -1, "error": "无效的JSON响应"}, sandbox_tried

        apple_status = res.get('status')
        if apple_status == 21007:
            sandbox_tried = True
            try:
                logger.info("[Order.apple.get_cert_rsp_from_apple] 生产环境返回21007，切换沙箱环境验证...")
                sandbox_response = session.post(settings.APPLE_VERIFY_URL_SANDBOX, json=data, timeout=timeout)
                sandbox_response.raise_for_status()
                res = sandbox_response.json()
                logger.info(f"沙箱环境验证成功，响应长度: {len(sandbox_response.content)}")
                return res, sandbox_tried
            except requests.exceptions.RequestException as e:
                logger.error(f"[Order.apple.get_cert_rsp_from_apple] 沙箱环境请求异常: {str(e)}")
                return {"status": -1, "error": str(e)}, sandbox_tried
            except json.JSONDecodeError as e:
                logger.error(f"[Order.apple.get_cert_rsp_from_apple] 沙箱环境响应解析失败: {str(e)}")
                return {"status": -1, "error": "无效的JSON响应"}, sandbox_tried

        logger.info(f"生产环境验证成功，响应长度: {len(response.content)}")
        return res, sandbox_tried
