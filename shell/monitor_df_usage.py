# coding=utf-8
import subprocess
import sys
import traceback

import requests


def send_mail(send_list: list):
    post_data = {
        "msg": str(send_list),
        "token": "v-MxkdFATpo-k2_mASaKx_token"
    }
    response = requests.post(
        "https://api.v3phone.xyz/v3sim/config/alertFoError/",
        json=post_data,
        timeout=10  # 添加超时设置
    )
    print(response.content)


if __name__ == '__main__':
    try:
        cmd = "df -h | sed -n '4p' | awk '{print $5}'"
        output, error = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
        usage_rate = output.decode('utf-8').replace("%", "")
        usage_rate = int(usage_rate)

        print(f"df usage rate: {usage_rate}")

        cmd = "free | awk 'NR==2{print $NF, $2, $3}'"
        output2, error2 = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE,
                                           stderr=subprocess.PIPE).communicate()
        mem_rate_str = output2.decode('utf-8').replace("%", "")
        mem_rate_split = str(mem_rate_str).split()
        avail_mem = int(mem_rate_split[0])
        full_mem = int(mem_rate_split[1])
        used_mem = int(mem_rate_split[2])
        mem_rate = (used_mem / full_mem) * 100
        print(f"mem used: {mem_rate}")

        cmd = "vmstat | awk '{print $13, $14}' | sed -n '$p'"
        output3, error3 = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE,
                                           stderr=subprocess.PIPE).communicate()
        cpu_rate_str = output3.decode('utf-8').replace("%", "")
        cpu_rate_split = str(cpu_rate_str).split()
        cpu_us = int(cpu_rate_split[0])
        cpu_sy = int(cpu_rate_split[1])
        cpu_rate = cpu_us + cpu_sy
        print(f"cpu rate: {cpu_rate}")

        if len(sys.argv) == 1:
            df_limit = 50
            mem_limit = 60
            cpu_limit = 40
        elif len(sys.argv) == 2:
            df_limit = int(sys.argv[1])
            mem_limit = 60
            cpu_limit = 40
        elif len(sys.argv) == 3:
            df_limit = int(sys.argv[1])
            mem_limit = int(sys.argv[2])
            cpu_limit = 40
        else:
            df_limit = int(sys.argv[1])
            mem_limit = int(sys.argv[2])
            cpu_limit = int(sys.argv[3])

        cmd = """ifconfig -a|grep inet|grep -v 127.0.0.1|grep -v inet6|awk '{print $2}'|tr -d "addr:" """
        output4, error4 = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE,
                                           stderr=subprocess.PIPE).communicate()
        ip = output4.decode('utf-8').replace("%", "").replace("\n", ", ")
        print(f"ip: {ip}")

        if usage_rate >= df_limit or mem_rate >= mem_limit or cpu_rate >= cpu_limit:
            alert_list = [
                "服务器状态告警",
                f"ip: {ip}",
                f"df usage rate: {usage_rate}",
                f"mem usage rate: {mem_rate}",
                f"cpu usage rate: {cpu_rate}"
            ]
            send_mail(alert_list)
    except Exception as e:
        traceback.print_exc()
        send_mail([str(e)])
