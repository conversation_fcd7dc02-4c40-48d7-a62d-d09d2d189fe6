# encoding=utf-8
import time

import pymysql
import requests
from pymysql import cursors


def send_mail(line: str):
    post_data = {
        "msg": line,
        "token": "v-MxkdFATpo-k2_mASaKx_token"
    }
    response = requests.post(
        "https://api.v3phone.xyz/v3sim/config/alertFoError/",
        json=post_data,
        timeout=10  # 添加超时设置
    )
    print(response.content)


def work():
    db = pymysql.connect(host='127.0.0.1', port=3306, user='root', passwd='123qweQWE', db='lamp', charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)
    t1 = time.time()

    cmd = "update User_usertraffic set usage_cnt=0;"
    print(cmd)
    cursor.execute(cmd)
    t2 = time.time()

    db.commit()
    cursor.close()
    db.close()

    send_mail(f"全体流量cnt清空，耗时：{t2 - t1}s")


if __name__ == '__main__':
    try:
        work()
    except Exception as e:
        send_mail(str(e))
