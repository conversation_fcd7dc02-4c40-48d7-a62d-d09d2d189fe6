# coding=utf-8
import datetime
import os
import subprocess
import sys
import traceback

import requests

bin_file = '/root/www/lamp/shell/monitor_error_log.bin'


def set_newest_row_number(row_number: int):
    print(f"set current row number: {row_number}")
    with open(bin_file, 'w') as f:
        f.write(f'{row_number}')


def get_last_handle_row_number() -> int:
    if not os.path.exists(bin_file):
        with open(bin_file, 'w') as f:
            f.write('1')

    with open(bin_file, 'r') as f:
        line = f.readline()
        line = line.strip()
        return int(line)


def get_current_row_number() -> int:
    cmd = "wc -l /root/www/lamp/logs/django.err"
    print(cmd)
    out = subprocess.getoutput(cmd)
    return int(out.split()[0])


def get_new_logs(begin_index: int, end_index: int) -> list:
    cmd = f"sed -n '{begin_index},{end_index}p' /root/www/lamp/logs/django.err"
    print(cmd)
    output, error = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    lines = output.decode("utf-8").split("\n")
    lines = [v for v in lines if v.strip()]
    return lines


def work(err_cnt_limit: int, warn_cnt_limit: int):
    current_row_number = get_current_row_number()
    last_row_number = get_last_handle_row_number()
    print(f"last_row_number: {last_row_number}, current_row_number: {current_row_number} ")
    if current_row_number != last_row_number or current_row_number == 1:
        # 一般不会颠倒，如果倒了，我们就重新开始
        if current_row_number < last_row_number:
            print(f"reset last_row_number = 1")
            last_row_number = 1

        lines = get_new_logs(last_row_number + 1, current_row_number)
        warn_lines = []
        error_lines = []

        for line in lines:
            # registerPushId 不要报警
            if '[WARNING]' in line:
                warn_lines.append(line)
            elif '[ERROR]' in line:
                if '贵人加点' in line or '用户反馈' in line:
                    pass
                else:
                    error_lines.append(line)

        print(f"warn error: {len(warn_lines)}")
        print(f"error error: {len(error_lines)}")
        print(f"err_cnt_limit: {err_cnt_limit}")
        print(f"warn_cnt_limit: {warn_cnt_limit}")

        if len(error_lines) > err_cnt_limit or len(warn_lines) >= warn_cnt_limit:
            print("send mail...")
            send_mail(error_lines + warn_lines)
            print("send mail finished.")

        # 更新row_number
        set_newest_row_number(current_row_number)


def send_mail(send_list: list):
    post_data = {
        "msg": str(send_list),
        "token": "v-MxkdFATpo-k2_mASaKx_token"
    }
    response = requests.post(
        "https://api.v3phone.xyz/v3sim/config/alertFoError/",
        json=post_data,
        timeout=10  # 添加超时设置
    )
    print(response.content)


if __name__ == '__main__':
    print("*" * 20)
    print(datetime.datetime.utcnow())
    print("*" * 20)
    try:
        error_cnt_limit = int(sys.argv[1])
        warn_cnt_limit = int(sys.argv[2])
        work(error_cnt_limit, warn_cnt_limit)
    except Exception as e:
        traceback.print_exc()
        send_mail([str(e)])
