# encoding=utf-8

import pymysql
import requests
from pymysql import cursors


def send_mail(line: str):
    post_data = {
        "msg": line,
        "token": "v-MxkdFATpo-k2_mASaKx_token"
    }
    response = requests.post(
        "https://api.v3phone.xyz/v3sim/config/alertFoError/",
        json=post_data,
        timeout=10  # 添加超时设置
    )
    print(response.content)


def work():
    db = pymysql.connect(host='127.0.0.1', port=3306, user='root', passwd='123qweQWE', db='lamp', charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 异常订单不要、退款订单不要
    cmd = "select * from Server_server;"
    print(cmd)
    cursor.execute(cmd)
    servers = cursor.fetchall()
    server_size = len(servers)
    print(f"handle size: {server_size}")

    available_cnt = 0
    unavailable_cnt = 0
    for idx, i in enumerate(servers):
        if i['available'] == 1:
            available_cnt += 1
        else:
            unavailable_cnt += 1
    db.commit()
    cursor.close()
    db.close()

    unavailable_rate = unavailable_cnt / (available_cnt + unavailable_cnt)
    if unavailable_rate >= 0.3:
        send_mail(f"服务器不可用超过：{unavailable_rate * 100}%")


if __name__ == '__main__':
    try:
        work()
    except Exception as e:
        send_mail(str(e))
