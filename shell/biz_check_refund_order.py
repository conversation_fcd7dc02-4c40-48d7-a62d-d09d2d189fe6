# encoding=utf-8

import datetime
import json
import queue
import smtplib
import sys
import threading
import traceback
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
import requests

send_list = []
todo_queue = queue.Queue()

s = requests.Session()


def do_batch_users(worker_name: str):
    try:
        index = 0
        while not todo_queue.empty():
            user_db = todo_queue.get()
            index += 1
            device_id = user_db['device_id']
            if index % 100 == 0:
                print(f"{worker_name}: check index: {index}, device_id:{device_id} ")
            r = s.get(f"http://127.0.0.1:8000/lpapi/order/check/?device_id={device_id}&token=watch-cat-hello")
            content = r.content.decode('utf-8')

            ret = json.loads(content)
            if ret['err_code'] == -1:
                print(ret)
                continue

            if ret['data']['refresh'] == 0:
                continue

            send_list.append(ret['data'])
    except Exception:
        traceback.print_exc()
        send_mail([])


def work(worker_cnt):
    db = pymysql.connect(host='127.0.0.1', port=3306, user='root', passwd='123qweQWE', db='lamp', charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 异常订单不要、退款订单不要
    cmd = "select device_id from Order_order;"
    print(cmd)
    cursor.execute(cmd)
    todo_list = cursor.fetchall()
    todo_size = len(todo_list)
    print(f"handle size: {todo_size}")

    db.commit()
    cursor.close()
    db.close()

    print("start produce ... ")
    threads = []
    for i in todo_list:
        todo_queue.put(i)

    print("start consume ... ")
    for i in range(worker_cnt):
        worker_name = "worker" + str(i)
        t = threading.Thread(target=do_batch_users, args=(worker_name,))
        threads.append(t)
        t.start()

    for i in threads:
        i.join()

    if len(send_list) > 0:
        open_2_open_cnt = 0
        open_2_closed_cnt_normal = 0
        open_2_closed_cnt_refund = 0
        closed_2_open_cnt = 0
        closed_2_closed_cnt = 0
        for i in send_list:
            if i['before_order_status'] == 'CLOSED' and i['order_status'] == 'OPEN':
                closed_2_open_cnt += 1
            if i['before_order_status'] == 'OPEN' and i['order_status'] == 'OPEN':
                open_2_open_cnt += 1
            elif i['before_order_status'] == 'CLOSED' and i['order_status'] == 'CLOSED':
                closed_2_closed_cnt += 1
            elif i['before_order_status'] == 'OPEN' and i['order_status'] == 'CLOSED':
                if i['before_expired_at'] == i['expired_at']:
                    open_2_closed_cnt_normal += 1
                else:
                    open_2_closed_cnt_refund += 1

        send_list.append(f"订单变化：{len(send_list)}个")
        send_list.append(f"之前关闭，现在打开：{closed_2_open_cnt}个")
        send_list.append(f"之前打开，现在打开：{open_2_open_cnt}个")
        send_list.append(f"之前打开，现在正常关闭：{open_2_closed_cnt_normal}个")
        send_list.append(f"之前打开，现在退款关闭：{open_2_closed_cnt_refund}个")
        send_list.append(f"之前关闭，现在关闭：{closed_2_closed_cnt}个")

    if send_list:
        send_mail(send_list)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header('[GBJD]用户退款警告 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>用户退款警告：{datetime.datetime.utcnow()}</h2>'
        html += """
         <table border="1">
          <tr>
             <th>index</th>
             <th>订单变化用户</th>
         </tr>
         """
        for index, send_item in enumerate(send_list):
            html += f"""
             <tr>
                 <td>{index}</td>
                 <td>{send_item}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()


if __name__ == '__main__':
    print("*" * 20)
    print(f"refund user, time: {datetime.datetime.utcnow()}")
    print("*" * 20)
    try:
        if len(sys.argv) == 2:
            worker_cnt = int(sys.argv[1])
        else:
            worker_cnt = 1
        work(worker_cnt)
    except Exception as e:
        traceback.print_exc()
        send_mail([str(e)])
