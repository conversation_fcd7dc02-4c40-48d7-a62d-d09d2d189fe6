# encoding=utf-8

import datetime
import json
import queue
import sys
import threading
import time
import traceback

import pymysql
import requests
from pymysql import cursors

send_list = []
todo_queue = queue.Queue()

s = requests.Session()


def do_batch_users(worker_name: str):
    try:
        index = 0
        device_ids = []
        while not todo_queue.empty():
            user_db = todo_queue.get()
            index += 1
            device_id = user_db['device_id']
            device_ids.append(device_id)

            if index > 0 and index % 30 == 0:
                print(f"################################")
                print(f"################################")
                print(f"{worker_name}: check index: {index}, size:{len(device_ids)}, device_ids:{device_ids}")
                r = s.post(f"http://127.0.0.1:8000/lpapi/order/batchcheck/", json={
                    "token": "watch-cat-hello",
                    "device_ids": device_ids
                })

                content = r.content.decode('utf-8')
                response = json.loads(content)
                if response['err_code'] != 0:
                    print(response)
                    time.sleep(3)
                    continue

                if "data" not in response or len(response["data"]) == 0:
                    print("req:", device_ids)
                    print("rsp:", response)

                for item in response["data"]:
                    if "err_code" in item:
                        print("content:", item)
                        continue
                    if item['refresh'] == 0:
                        continue
                    send_list.append(item)

                # 10个一批，处理后清空
                device_ids = []

                time.sleep(3)

    except Exception:
        traceback.print_exc()
        send_mail([])


def work(worker_cnt):
    db = pymysql.connect(host='127.0.0.1', port=3306, user='root', passwd='123qweQWE', db='lamp', charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 异常订单不要、退款订单不要
    cmd = (
        "select device_id from Order_order where valid=1 and (     (expired_at < NOW() AND order_status = 'OPEN')     OR     (expired_at < now() and expired_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) ) order by id desc;")
    print(cmd)
    cursor.execute(cmd)
    todo_list = cursor.fetchall()
    todo_size = len(todo_list)
    print(f"handle size: {todo_size}")

    db.commit()
    cursor.close()
    db.close()

    print("start produce ... ")
    threads = []
    for i in todo_list:
        todo_queue.put(i)

    print("start consume ... ")
    for i in range(worker_cnt):
        worker_name = "worker" + str(i)
        t = threading.Thread(target=do_batch_users, args=(worker_name,))
        threads.append(t)
        t.start()

    for i in threads:
        i.join()

    if len(send_list) > 0:
        open_2_open_cnt = 0
        open_2_closed_cnt_normal = 0
        open_2_closed_cnt_refund = 0
        closed_2_open_cnt = 0
        closed_2_closed_cnt = 0
        for i in send_list:
            if i['before_order_status'] == 'CLOSED' and i['order_status'] == 'OPEN':
                closed_2_open_cnt += 1
            if i['before_order_status'] == 'OPEN' and i['order_status'] == 'OPEN':
                open_2_open_cnt += 1
            elif i['before_order_status'] == 'CLOSED' and i['order_status'] == 'CLOSED':
                closed_2_closed_cnt += 1
            elif i['before_order_status'] == 'OPEN' and i['order_status'] == 'CLOSED':
                if i['before_expired_at'] == i['expired_at']:
                    open_2_closed_cnt_normal += 1
                else:
                    open_2_closed_cnt_refund += 1

        send_list.append(f"订单变化：{len(send_list)}个")
        send_list.append(f"之前关闭，现在打开：{closed_2_open_cnt}个")
        send_list.append(f"之前打开，现在打开：{open_2_open_cnt}个")
        send_list.append(f"之前打开，现在正常关闭：{open_2_closed_cnt_normal}个")
        send_list.append(f"之前打开，现在退款关闭：{open_2_closed_cnt_refund}个")
        send_list.append(f"之前关闭，现在关闭：{closed_2_closed_cnt}个")

    if send_list:
        send_mail(send_list)


def send_mail(send_list: list):
    post_data = {
        "msg": str(send_list),
        "token": "v-MxkdFATpo-k2_mASaKx_token"
    }
    response = requests.post(
        "https://api.v3phone.xyz/v3sim/config/alertFoError/",
        json=post_data,
        timeout=10  # 添加超时设置
    )
    print(response.content)


if __name__ == '__main__':
    print("*" * 20)
    print(f"refund user, time: {datetime.datetime.utcnow()}")
    print("*" * 20)
    try:
        if len(sys.argv) == 2:
            worker_cnt = int(sys.argv[1])
        else:
            worker_cnt = 1
        work(worker_cnt)
    except Exception as e:
        traceback.print_exc()
        send_mail([str(e)])
