# encoding=utf-8
import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr
from typing import Dict, List, Tuple, Any

import pymysql
import pymysql.cursors


class DatabaseManager:
    def __init__(self, host='127.0.0.1', port=3306, user='root', passwd='123qweQWE', db='lamp'):
        self.connection_params = {
            'host': host,
            'port': port,
            'user': user,
            'passwd': passwd,
            'db': db,
            'charset': 'utf8'
        }
        self.db = None
        self.cursor = None

    def connect(self):
        self.db = pymysql.connect(**self.connection_params)
        self.cursor = self.db.cursor(pymysql.cursors.DictCursor)
        return self.db, self.cursor

    def close(self):
        if self.cursor:
            self.cursor.close()
        if self.db:
            self.db.close()

    def execute_query(self, query: str) -> List[Dict]:
        print(query)
        self.cursor.execute(query)
        results = self.cursor.fetchall()
        return results

    def commit(self):
        if self.db:
            self.db.commit()


class UserDataFetcher:
    def __init__(self, db_manager: DatabaseManager, target_date: datetime.date = None):
        self.db_manager = db_manager
        # 如果没有指定日期，默认使用昨天
        if target_date is None:
            self.target_date = datetime.datetime.now(datetime.timezone.utc).date() + datetime.timedelta(days=-1)
        else:
            self.target_date = target_date
        self.target_date_str = self.target_date.strftime("%Y-%m-%d")
        print(f"目标日期: {self.target_date_str}")

    def get_all_baipiao_users(self) -> List[Dict]:
        """获取所有白嫖用户（试用期<=5天的用户）"""
        query = """
        select device_id, expired_at, created_at as order_created_at from Order_order 
        where valid=1 and expiration_intent not in (5,6) and DATEDIFF(expired_at, created_at) <= 5 
        """
        users = self.db_manager.execute_query(query)
        print(f"all baipiao size: {len(users)}")
        return users

    def get_all_user_cnt(self) -> int:
        """获取总用户数"""
        query = "select count(*) as user_cnt from User_user"
        users = self.db_manager.execute_query(query)
        return users[0]['user_cnt']

    def get_all_pay_user(self) -> List[Dict]:
        """获取所有付费用户"""
        query = """
        select device_id, expired_at, created_at, created_at as order_created_at from Order_order 
        where valid=1 and order_status='OPEN'
        """
        users = self.db_manager.execute_query(query)
        print(f"all pay user size: {len(users)}")
        return users

    def get_calc_pay_user(self) -> List[Dict]:
        """获取需要计算的付费用户"""
        # 计算日期范围：-4天到+1天
        start_date = self.target_date + datetime.timedelta(days=-4)
        end_date = self.target_date + datetime.timedelta(days=1)

        start_date_str = start_date.strftime("%Y-%m-%d")
        end_date_str = end_date.strftime("%Y-%m-%d")

        query = f"""
        select device_id, expired_at, created_at, created_at as order_created_at from Order_order 
        where valid=1 and order_status='OPEN'
        and DATE(created_at) >= '{start_date_str}' and DATE(created_at) < '{end_date_str}'
        """
        users = self.db_manager.execute_query(query)
        print(f"计算日期范围 {start_date_str} 到 {end_date_str} 的付费用户数: {len(users)}")
        return users

    def get_all_refund_users(self) -> List[Dict]:
        """获取所有退款用户"""
        query = "select device_id, created_at from Order_order where expiration_intent=5;"
        users = self.db_manager.execute_query(query)
        print(f"all refund size: {len(users)}")
        return users

    def get_target_date_new_subscribers(self) -> List[Dict]:
        """获取目标日期新订阅用户（首次订阅）"""
        query = f"""
        select device_id, expired_at, created_at, created_at as order_created_at from Order_order 
        where valid=1 and expiration_intent not in (5,6) 
        and DATE(created_at) = '{self.target_date_str}'
        """
        users = self.db_manager.execute_query(query)
        print(f"{self.target_date_str} new subscribers size: {len(users)}")
        return users

    def get_target_date_renewals(self) -> List[Dict]:
        """获取目标日期续订用户（非首次订阅）"""
        # 查找目标日期有续订行为的用户（通过original_transaction_id判断是否为续订）
        query = f"""
        select o1.device_id, o1.expired_at, o1.created_at, o1.created_at as order_created_at 
        from Order_order o1
        inner join (
            select device_id, min(created_at) as first_subscription_date
            from Order_order 
            where valid=1 and expiration_intent not in (5,6)
            group by device_id
        ) o2 on o1.device_id = o2.device_id
        where o1.valid=1 and o1.expiration_intent not in (5,6)
        and DATE(o1.created_at) = '{self.target_date_str}'
        and o1.created_at > o2.first_subscription_date
        """
        users = self.db_manager.execute_query(query)
        print(f"{self.target_date_str} renewals size: {len(users)}")
        return users

    def get_conversion_data(self) -> Dict[str, Any]:
        """获取转换率数据"""
        # 获取目标日期3天前的数据
        three_days_before_target = self.target_date + datetime.timedelta(days=-3)
        three_days_before_str = three_days_before_target.strftime("%Y-%m-%d")

        # 获取3天前的新订阅用户数
        query_new = f"""
        select count(*) as new_subscribers from Order_order 
        where valid=1 and expiration_intent not in (5,6) 
        and DATE(created_at) = '{three_days_before_str}'
        """
        new_subscribers = self.db_manager.execute_query(query_new)[0]['new_subscribers']

        # 获取这些用户中续订的用户数
        query_renewed = f"""
        select count(distinct o1.device_id) as renewed_users
        from Order_order o1
        inner join (
            select device_id, min(created_at) as first_subscription_date
            from Order_order 
            where valid=1 and expiration_intent not in (5,6)
            and DATE(created_at) = '{three_days_before_str}'
            group by device_id
        ) o2 on o1.device_id = o2.device_id
        where o1.valid=1 and o1.expiration_intent not in (5,6)
        and o1.created_at > o2.first_subscription_date
        """
        renewed_users = self.db_manager.execute_query(query_renewed)[0]['renewed_users']

        conversion_rate = round((renewed_users / new_subscribers * 100) if new_subscribers > 0 else 0, 2)

        return {
            'new_subscribers': new_subscribers,
            'renewed_users': renewed_users,
            'conversion_rate': conversion_rate,
            'date': three_days_before_str
        }

    def get_trial_expired_users(self) -> List[Dict]:
        """获取过了试用期的用户（目标日期3天前的新订阅用户）"""
        # 获取目标日期3天前的数据
        three_days_before_target = self.target_date + datetime.timedelta(days=-3)
        three_days_before_str = three_days_before_target.strftime("%Y-%m-%d")

        # 获取3天前的新订阅用户，这些用户在目标日期已经过了试用期
        query = f"""
        select o1.device_id, o1.expired_at, o1.created_at, o1.created_at as order_created_at 
        from Order_order o1
        inner join (
            select device_id, min(created_at) as first_subscription_date
            from Order_order 
            where valid=1 and expiration_intent not in (5,6)
            and DATE(created_at) = '{three_days_before_str}'
            group by device_id
        ) o2 on o1.device_id = o2.device_id
        where o1.valid=1 and o1.expiration_intent not in (5,6)
        and o1.created_at = o2.first_subscription_date
        """
        users = self.db_manager.execute_query(query)
        print(f"{three_days_before_str} 新订阅用户（已过试用期）size: {len(users)}")
        return users

    def get_trial_expired_renewals(self) -> List[Dict]:
        """获取过了试用期且续订的用户"""
        # 获取目标日期3天前的数据
        three_days_before_target = self.target_date + datetime.timedelta(days=-3)
        three_days_before_str = three_days_before_target.strftime("%Y-%m-%d")

        # 获取3天前的新订阅用户，且当前订单状态为OPEN的用户（说明过了试用期还续订了）
        query = f"""
        select device_id, expired_at, created_at, created_at as order_created_at 
        from Order_order 
        where valid=1 and expiration_intent not in (5,6)
        and order_status='OPEN'
        and DATE(created_at) = '{three_days_before_str}'
        """
        users = self.db_manager.execute_query(query)
        print(f"{three_days_before_str} 新订阅用户续订（已过试用期）size: {len(users)}")
        return users


class UserAnalyzer:
    @staticmethod
    def categorize_users_by_subscription(user_list: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """
        expired_at - created_at <= 3天是试用用户= month
        expired_at - created_at >= 4天是跳过试用期的week用户，它是直接扣费的
        expired_at - created_at > 7天是完成了试用的month用户
        """
        # 试用用户（<=3天）= month
        month_pay_users = [v for v in user_list if 0 < (v['expired_at'] - v['order_created_at']).days <= 3]

        # 跳过试用期的week用户（>=4天且<=7天）= week
        week_pay_users = [v for v in user_list if 4 <= (v['expired_at'] - v['order_created_at']).days <= 7]

        # 完成了试用的month用户（>7天）= month
        completed_month_users = [v for v in user_list if (v['expired_at'] - v['order_created_at']).days > 7]

        # 合并所有month用户
        month_pay_users.extend(completed_month_users)

        print(f"试用用户(month): {len(month_pay_users) - len(completed_month_users)}")
        print(f"跳过试用期用户(week): {len(week_pay_users)}")
        print(f"完成试用用户(month): {len(completed_month_users)}")
        print(f"总week用户: {len(week_pay_users)}")
        print(f"总month用户: {len(month_pay_users)}")

        return week_pay_users, month_pay_users

    @staticmethod
    def get_target_date_stats(week_users: List[Dict], month_users: List[Dict], target_date: datetime.date) -> Dict[
        str, Any]:
        target_date_start = target_date
        target_date_end = target_date + datetime.timedelta(days=1)
        target_date_start_dt = datetime.datetime.combine(target_date_start, datetime.time.min)
        target_date_end_dt = datetime.datetime.combine(target_date_end, datetime.time.min)

        print("target_date_start_dt:", target_date_start_dt)
        print("target_date_end_dt:", target_date_end_dt)

        target_week_count = len(
            [v for v in week_users if target_date_start_dt <= v['order_created_at'] < target_date_end_dt])
        target_month_count = len(
            [v for v in month_users if target_date_start_dt <= v['order_created_at'] < target_date_end_dt])
        target_week_sum = round(target_week_count * 0.49, 2)
        target_month_sum = round(target_month_count * 0.99, 2)

        print("target_week_count:", target_week_count)
        print("target_month_count:", target_month_count)
        print("target_week_income_sum:", target_week_sum)
        print("target_month_income_sum:", target_month_sum)

        return {
            'target_date_start_dt': target_date_start_dt,
            'target_date_end_dt': target_date_end_dt,
            'target_week_count': target_week_count,
            'target_month_count': target_month_count,
            'target_week_sum': target_week_sum,
            'target_month_sum': target_month_sum
        }

    @staticmethod
    def calculate_total_income(week_count: int, month_count: int) -> Dict[str, float]:
        week_sum = round(week_count * 0.49, 2)
        month_sum = round(month_count * 0.99, 2)
        total_sum = round(week_sum + month_sum, 2)
        return {
            'week_sum': week_sum,
            'month_sum': month_sum,
            'total_sum': total_sum
        }


class ReportGenerator:
    @staticmethod
    def generate_html_report(total_user_cnt: int,
                             week_users: List[Dict], month_users: List[Dict],
                             baipiao_users: List[Dict], refund_users: List[Dict],
                             target_stats: Dict[str, Any], income_stats: Dict[str, float],
                             target_new_subscribers: List[Dict], target_renewals: List[Dict],
                             conversion_data: Dict[str, Any], target_date: datetime.date,
                             trial_expired_users: List[Dict], trial_expired_renewals: List[Dict],
                             trial_expired_stats: Dict[str, Any]) -> str:
        """生成HTML格式的报告"""
        target_date_str = target_date.strftime("%Y-%m-%d")
        html = f'<html><body><h1>{target_date_str} 用户统计报告</h1>'

        # 用户总览表格
        html += ReportGenerator._generate_user_overview_table(
            total_user_cnt,
            len(week_users), len(month_users),
            len(baipiao_users), len(refund_users)
        )

        # 目标日期订阅总览表格
        html += ReportGenerator._generate_target_date_subscription_table(
            target_new_subscribers, target_renewals, conversion_data, target_date_str
        )

        # 目标日期付费总览表格
        html += ReportGenerator._generate_target_date_overview_table(target_stats, target_date_str)

        # 收入表格
        html += ReportGenerator._generate_income_table(income_stats)

        # 过了试用期的用户数据
        html += ReportGenerator._generate_trial_expired_users_table(trial_expired_users, trial_expired_renewals,
                                                                    trial_expired_stats)

        html += '</body></html>'
        return html

    @staticmethod
    def _generate_user_overview_table(total_user_cnt: int, week_count: int, month_count: int,
                                      baipiao_count: int, refund_count: int) -> str:
        """生成用户总览表格"""
        return f"""
        <h2>总览</h2>
        <table border="1">
         <tr>
            <th>总用户</th>
            <th>VIP用户</th>
            <th>周度用户数</th>
            <th>月度用户数</th>
            <th>白嫖用户</th>
            <th>退款用户</th>
        </tr>
        <tr>
            <td>{total_user_cnt}</td>
            <td>{week_count + month_count}</td>
            <td>{week_count}</td>
            <td>{month_count}</td>
            <td>{baipiao_count}</td>
            <td>{refund_count}</td>
        </tr>
        </table>
        """

    @staticmethod
    def _generate_target_date_subscription_table(target_new_subscribers: List[Dict],
                                                 target_renewals: List[Dict],
                                                 conversion_data: Dict[str, Any],
                                                 target_date_str: str) -> str:
        """生成目标日期订阅总览表格"""
        new_subscribers_count = len(target_new_subscribers)
        renewals_count = len(target_renewals)

        return f"""
        <h2>{target_date_str} 订阅总览</h2>
        <table border="1">
         <tr>
            <th>{target_date_str} 新订阅用户</th>
            <th>{target_date_str} 续订用户</th>
            <th>{target_date_str} 总订阅用户</th>
            <th>{conversion_data['date']} 新订阅用户</th>
            <th>{conversion_data['date']} 续订用户</th>
            <th>3天转换率</th>
        </tr>
        <tr>
            <td>{new_subscribers_count}</td>
            <td>{renewals_count}</td>
            <td>{new_subscribers_count + renewals_count}</td>
            <td>{conversion_data['new_subscribers']}</td>
            <td>{conversion_data['renewed_users']}</td>
            <td>{conversion_data['conversion_rate']}%</td>
        </tr>
        </table>
        <p><strong>说明：</strong>新订阅用户指首次订阅（3天免费试用），续订用户指试用期后继续付费的用户。只有续订用户才计入实际收入。转换率基于{conversion_data['date']}的新订阅用户计算。</p>
        """

    @staticmethod
    def _generate_target_date_overview_table(stats: Dict[str, Any], target_date_str: str) -> str:
        """生成目标日期付费总览表格"""
        total_income = round(
            stats['target_week_sum'] + stats['target_month_sum'], 2
        )

        return f"""
        <h2>{target_date_str} 付费总览</h2>
        <table border="1">
         <tr>
            <th>{target_date_str} 总</th>
            <th>{target_date_str} VIP</th>
            <th>{target_date_str} 月度VIP</th>
            <th>{target_date_str} 周度VIP</th>
            <th>{target_date_str} 月度用户数</th>
            <th>{target_date_str} 周度用户数</th>
        </tr>
        <tr>
            <td>{total_income}</td>
            <td>{total_income}</td>
            <td>{stats['target_month_sum']}</td>
            <td>{stats['target_week_sum']}</td>
            <td>{stats['target_month_count']}</td>
            <td>{stats['target_week_count']}</td>
        </tr>
        </table>
        """

    @staticmethod
    def _generate_income_table(income: Dict[str, float]) -> str:
        """生成收入表格"""
        return f"""
        <h2>收入统计</h2>
        <table border="1">
         <tr>
            <th>周度收入</th>
            <th>月度收入</th>
            <th>总收入</th>
        </tr>
        <tr>
            <td>${income['week_sum']}</td>
            <td>${income['month_sum']}</td>
            <td>${income['total_sum']}</td>
        </tr>
        </table>
        """

    @staticmethod
    def _generate_trial_expired_users_table(trial_expired_users: List[Dict],
                                            trial_expired_renewals: List[Dict],
                                            trial_expired_stats: Dict[str, Any]) -> str:
        """生成过了试用期的用户表格"""
        html = f"""
        <h2>过了试用期的用户</h2>
        <table border="1">
         <tr>
            <th>过了试用期的用户</th>
            <th>过了试用期的续订用户</th>
            <th>过了试用期的收入</th>
        </tr>
        <tr>
            <td>{len(trial_expired_users)}</td>
            <td>{len(trial_expired_renewals)}</td>
            <td>{round(trial_expired_stats['target_week_sum'] + trial_expired_stats['target_month_sum'], 2)}</td>
        </tr>
        </table>
        """
        return html


class EmailSender:
    def __init__(self, smtp_server='127.0.0.1', smtp_port=587,
                 from_addr='<EMAIL>', password='cptbtptp123ASD',
                 to_addr=None):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.from_addr = from_addr
        self.password = password
        self.to_addr = to_addr or ['<EMAIL>', '<EMAIL>', '<EMAIL>']

    def send_report(self, subject: str, html_content: str) -> bool:
        """发送HTML格式的报告邮件"""
        try:
            # 邮件对象
            msg = MIMEMultipart()
            msg['From'] = self._format_addr(f'发件人 <{self.from_addr}>')
            msg['To'] = self._format_addr(f'收件人 <{self.to_addr}>')
            msg['Subject'] = Header(subject, 'utf-8').encode()

            msg.attach(MIMEText(html_content, 'html', 'utf-8'))

            print("smtp_server:", self.smtp_server)
            print("smtp_port:", self.smtp_port)
            print("from_addr:", self.from_addr)
            print("password:", self.password)

            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            print(" server.starttls()...")
            server.starttls()  # 启用TLS加密
            print(" server.login()...")
            server.login(self.from_addr, self.password)
            print(" server.sendmail()...")
            server.sendmail(self.from_addr, self.to_addr, msg.as_string())
            print(" server.quit()...")
            server.quit()
            return True
        except Exception:
            traceback.print_exc()
            return False

    @staticmethod
    def _format_addr(s):
        name, addr = parseaddr(s)
        return formataddr((Header(name, 'utf-8').encode(), addr))


class DataReporter:
    def __init__(self, db_manager: DatabaseManager, target_date: datetime.date = None):
        self.db_manager = db_manager
        self.target_date = target_date or (
                datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=-1)).date()
        self.target_date_str = self.target_date.strftime("%Y-%m-%d")

    def save_report_to_db(self, data: Dict[str, Any]) -> bool:
        """将报告数据保存到数据库"""
        try:
            sql = self._generate_insert_sql(data)
            print(sql)
            self.db_manager.cursor.execute(sql)
            self.db_manager.commit()
            return True
        except Exception:
            traceback.print_exc()
            return False

    def _generate_insert_sql(self, data: Dict[str, Any]) -> str:
        """生成插入SQL"""
        fields = [
            'day', 'today_vip_income', 'today_vip_week_income', 'today_vip_month_income',
            'today_vip_user_cnt', 'today_vip_week_user_cnt', 'today_vip_month_user_cnt',
            'today_baipiao_user_cnt', 'today_refund_user_cnt',
            'today_new_subscribers', 'today_renewals', 'conversion_rate',
            'trial_expired_users', 'trial_expired_renewals', 'trial_expired_income'
        ]

        values = [
            f"'{self.target_date_str}'",
            f"'{data['today_vip_income']}'",
            f"'{data['today_vip_week_income']}'",
            f"'{data['today_vip_month_income']}'",
            f"'{data['today_vip_user_cnt']}'",
            f"'{data['today_vip_week_user_cnt']}'",
            f"'{data['today_vip_month_user_cnt']}'",
            f"'{data['today_baipiao_user_cnt']}'",
            f"'{data['today_refund_user_cnt']}'",
            f"'{data['today_new_subscribers']}'",
            f"'{data['today_renewals']}'",
            f"'{data['conversion_rate']}'",
            f"'{data['trial_expired_users']}'",
            f"'{data['trial_expired_renewals']}'",
            f"'{data['trial_expired_income']}'"
        ]

        update_clauses = [f"`{field}` = VALUES(`{field}`)" for field in fields]

        sql = f"""
            INSERT INTO `Report_finance_new` (
                {', '.join([f'`{field}`' for field in fields])}
            )
            VALUES (
                {', '.join(values)}
            ) ON DUPLICATE KEY UPDATE {', '.join(update_clauses)};
        """

        return sql


def prepare_report_data(baipiao_users: List[Dict],
                        refund_users: List[Dict], target_new_subscribers: List[Dict],
                        target_renewals: List[Dict], conversion_data: Dict[str, Any], trial_expired_users: List[Dict],
                        trial_expired_renewals: List[Dict], trial_expired_stats: Dict[str, Any]) -> Dict[str, Any]:
    """准备报告数据"""

    # 计算过了试用期的实际收入
    trial_expired_income = round(
        trial_expired_stats['target_week_sum'] + trial_expired_stats['target_month_sum'], 2
    )

    return {
        'today_vip_income': trial_expired_income,
        'today_vip_week_income': trial_expired_stats['target_week_sum'],
        'today_vip_month_income': trial_expired_stats['target_month_sum'],
        'today_vip_user_cnt': trial_expired_stats['target_week_count'] + trial_expired_stats['target_month_count'],
        'today_vip_week_user_cnt': trial_expired_stats['target_week_count'],
        'today_vip_month_user_cnt': trial_expired_stats['target_month_count'],
        'today_baipiao_user_cnt': len(baipiao_users),
        'today_refund_user_cnt': len(refund_users),
        'today_new_subscribers': len(target_new_subscribers),
        'today_renewals': len(target_renewals),
        'conversion_rate': conversion_data['conversion_rate'],
        # 新增字段：过了试用期的用户数据
        'trial_expired_users': len(trial_expired_users),
        'trial_expired_renewals': len(trial_expired_renewals),
        'trial_expired_income': trial_expired_income,
    }


def work(target_date: datetime.date = None):
    """主函数"""
    try:
        # 初始化数据库连接
        print("初始化数据库连接...")
        db_manager = DatabaseManager()
        db_manager.connect()

        # 获取用户数据
        print("获取用户数据...")
        user_fetcher = UserDataFetcher(db_manager, target_date)
        total_user_cnt = user_fetcher.get_all_user_cnt()
        pay_users = user_fetcher.get_all_pay_user()
        calc_pay_users = user_fetcher.get_calc_pay_user()
        baipiao_users = user_fetcher.get_all_baipiao_users()
        refund_users = user_fetcher.get_all_refund_users()
        print("pay_users size:", len(pay_users))
        print("calc_pay_users size:", len(calc_pay_users))

        # 获取目标日期订阅数据
        print(f"获取{user_fetcher.target_date_str}订阅数据...")
        target_new_subscribers = user_fetcher.get_target_date_new_subscribers()
        target_renewals = user_fetcher.get_target_date_renewals()
        conversion_data = user_fetcher.get_conversion_data()

        # 获取过了试用期的用户数据（用于计算实际收入）
        print(f"获取过了试用期的用户数据...")
        trial_expired_users = user_fetcher.get_trial_expired_users()
        trial_expired_renewals = user_fetcher.get_trial_expired_renewals()

        # 分析用户数据
        print("分析用户数据...")
        analyzer = UserAnalyzer()
        week_users, month_users = analyzer.categorize_users_by_subscription(calc_pay_users)
        target_stats = analyzer.get_target_date_stats(week_users, month_users, user_fetcher.target_date)
        income_stats = analyzer.calculate_total_income(
            len(week_users), len(month_users)
        )

        # 分析过了试用期的用户数据（用于计算实际收入）
        print("分析过了试用期的用户数据...")
        trial_expired_week_users, trial_expired_month_users = analyzer.categorize_users_by_subscription(
            trial_expired_renewals)
        trial_expired_stats = analyzer.get_target_date_stats(trial_expired_week_users, trial_expired_month_users,
                                                             user_fetcher.target_date)

        # 生成报告
        print("生成报告...")
        report_generator = ReportGenerator()
        html_report = report_generator.generate_html_report(
            total_user_cnt, week_users, month_users,
            baipiao_users, refund_users, target_stats, income_stats,
            target_new_subscribers, target_renewals, conversion_data, user_fetcher.target_date,
            trial_expired_users, trial_expired_renewals, trial_expired_stats
        )

        # 准备报告数据
        print("准备报告数据...")
        report_data = prepare_report_data(
            baipiao_users, refund_users,
            target_new_subscribers, target_renewals, conversion_data,
            trial_expired_users, trial_expired_renewals, trial_expired_stats
        )

        # 保存报告数据到数据库
        print("保存报告数据到数据库...")
        data_reporter = DataReporter(db_manager, user_fetcher.target_date)
        data_reporter.save_report_to_db(report_data)


    finally:
        # 关闭数据库连接
        if 'db_manager' in locals():
            db_manager.close()


if __name__ == '__main__':
    try:
        # 支持命令行参数指定日期
        if len(sys.argv) > 1:
            date_str = sys.argv[1]
            try:
                # 支持多种日期格式
                if '.' in date_str:  # MM.DD 格式
                    date_str = f"2024-{date_str.replace('.', '-')}"
                elif len(date_str) == 8:  # YYYYMMDD 格式
                    date_str = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"

                target_date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
                print(f"指定日期: {target_date}")
            except ValueError:
                print(f"日期格式错误: {date_str}")
                print("支持的格式: YYYY-MM-DD, MM.DD, YYYYMMDD")
                sys.exit(1)
        else:
            target_date = None  # 默认使用昨天

        print(f"start get all pay user: {datetime.datetime.now(datetime.timezone.utc)}")
        work(target_date)
    except Exception:
        traceback.print_exc()
        # 发送空报告邮件，通知出错
        email_sender = EmailSender()
        email_sender.send_report('[GBJD]Pay用户汇总出错 ！！！', '<html><body><h1>脚本执行出错</h1></body></html>')
