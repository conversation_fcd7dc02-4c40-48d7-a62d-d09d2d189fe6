# encoding=utf-8
import pymysql
from pymysql import cursors


def work():
    db = pymysql.connect(host='127.0.0.1', port=3306, user='root', passwd='123qweQWE', db='lamp', charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 异常订单不要、退款订单不要
    cmd = "select * from User_usertraffic where device_id=ray_id;"
    print(cmd)
    cursor.execute(cmd)
    todo_list = cursor.fetchall()
    todo_size = len(todo_list)
    print(f"handle size: {todo_size}")

    for idx, i in enumerate(todo_list):
        if idx % 100 == 0:
            print(f"index: {idx} ###############")
            print(f"index: {idx} ###############")
            print(f"index: {idx} ###############")

        ray_id = i['ray_id']
        get_user_sql = f"select * from User_user where ray_id='{ray_id}'"
        print(get_user_sql)
        cursor.execute(get_user_sql)
        user = cursor.fetchone()
        if user:
            device_id = user["device_id"]
            update_user_sql = f"update User_usertraffic set device_id='{device_id}' where ray_id='{ray_id}'"
            print(update_user_sql)
            cursor.execute(update_user_sql)

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    work()
