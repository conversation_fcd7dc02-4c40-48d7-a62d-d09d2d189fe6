# encoding=utf-8
import json

import pymysql
import requests


def work():
    db = pymysql.connect(host='127.0.0.1', port=3306, user='root', passwd='123qweQWE', db='lamp', charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    cmd = "select * from Server_server;"
    print(cmd)
    cursor.execute(cmd)
    servers = cursor.fetchall()
    server_size = len(servers)
    print(f"handle size: {server_size}")

    all_cnt = server_size
    enabled_cnt = 0
    disabled_cnt = 0
    available_cnt = 0
    unavailable_cnt = 0

    for i in servers:
        if i["enabled"] == 0:
            disabled_cnt += 1
        elif i["enabled"] == 1:
            enabled_cnt += 1

        if i["available"] == 1:
            available_cnt += 1
        else:
            unavailable_cnt += 1
    cursor.close()
    db.close()

    notify_data = {
        "all_server_cnt": all_cnt,
        "enabled_server_cnt": enabled_cnt,
        "disabled_server_cnt": disabled_cnt,
        "available_cnt": available_cnt,
        "unavailable_cnt": unavailable_cnt,
    }
    send_mail(json.dumps(notify_data))


def send_mail(data: str):
    post_data = {
        "msg": data,
        "token": "v-MxkdFATpo-k2_mASaKx_token"
    }
    response = requests.post(
        "https://api.v3phone.xyz/v3sim/config/alertFoError/",
        json=post_data,
        timeout=10  # 添加超时设置
    )
    print(response.content)


if __name__ == '__main__':
    try:
        work()
    except Exception as e:
        send_mail(str(e))
