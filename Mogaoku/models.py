from django.db import models
from django.contrib import admin
from admin_totals.admin import ModelAdminTotals


class MogaokuInfo(models.Model):
    user_id = models.IntegerField("user id")
    user_name = models.Char<PERSON>ield("user name", max_length=100)
    token = models.Char<PERSON>ield('token', max_length=500)
    mobile = models.CharField('mobile', max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


@admin.register(MogaokuInfo)
class MogaokuInfoAdmin(ModelAdminTotals):
    files = ('user_id', 'user_name', 'token', 'mobile', 'updated_at')
    list_display = ('user_id', 'user_name', 'token', 'mobile', 'updated_at')
    list_display_link = ('user_id',)
    list_filter = ('user_name',)
    list_per_page = 50
