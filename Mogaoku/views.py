
from Common.views import CommonView
from Mogaoku.models import MogaokuInfo


# POST /mogaoku/token/report/ {'user_id', 'user_name', 'mobile', 'token', 'secret'}
class ReportInfo(CommonView):

    def __init__(self):
        self.secret = 'MksdYf-Dsf#s-NRTxL-WAz23X'

    def post(self, request):
        data, err_code, err_msg = self.ReadPostJson(request, ('user_id', 'user_name', 'mobile', 'token', 'secret'))
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        if data['secret'] != self.secret:
            return self.ReturnError('0000', 'wrong secret')

        del(data['secret'])

        info = self.GetOrNone(MogaokuInfo, user_id=data['user_id'])

        if info is None:
            info = MogaokuInfo(**data)

        else:
            info.user_name = data['user_name']
            info.mobile = data['mobile']
            info.token = data['token']

        info.save()
        return self.ReturnSuccess()



# POST /mogaoku/token/get/ {'user_id', 'secret'}
class GetToken(CommonView):

    def __init__(self):
        self.secret = 'MksdYf-Dsf#s-NRTxL-WAz23X'

    def post(self, request):
        data, err_code, err_msg = self.ReadPostJson(request, ('user_id', 'secret'))
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        if data['secret'] != self.secret:
            return self.ReturnError('0000', 'wrong secret')

        info = self.GetOrNone(MogaokuInfo, user_id=data['user_id'])

        if info is not None:
            return self.ReturnSuccess({'user_id': info.user_id, 'token': info.token})

        return self.ReturnSuccess()



