<html>
<script src="/js/jquery-1.8.3.min.js"></script>
<script src="/js/highcharts.js"></script>
<script src="/js/exporting.js"></script>


<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts-gl/dist/echarts-gl.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts-stat/dist/ecStat.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts/dist/extension/dataTool.min.js"></script>


<script>
    // 函数用于切换图例的显示状态
    function toggleLegend(containerId) {
        var chart = $('#' + containerId).highcharts(); // 获取对应的图表对象
        var series = chart.series[0];
        if (series.visible) {
            $(chart.series).each(function(){
                this.setVisible(false, false);
            });
            chart.redraw();
        } else {
            $(chart.series).each(function(){
                this.setVisible(true, false);
            });
            chart.redraw();
        }
    }
</script>


<script>
    $(function () {
        $('#container_subscription').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '用户每日订阅情况 {{appid}}'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: 'user count'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  order_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}

                {
                name: "user register",
                data: {{register}}
                }
            ]
        });
    });

</script>
<div id="container_subscription" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_subscription')">Toggle Legend</button>
</div>


<script>

    $(function () {
        $('#container_24h_online').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: 'Latest 24H online'
            },
            xAxis: {
                categories: {{ts|safe}}
            },
        yAxis: {
            title: {
                text: 'user online'
            }
        }
    ,
        plotOptions: {
            line: {
                dataLabels: {
                    enabled: true          // 开启数据标签
                }
            ,
                enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
            }
        }
    ,
        series: [
        {
            name: '({{server_ray_vip}} ray_servers) ({{server_unavailable}} unavailable) ',
            data: {{online_cnt}}
        }
    ]
    })
        ;
    });


</script>
<div id="container_24h_online" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_24h_online')">Toggle Legend</button>
</div>


</html>
