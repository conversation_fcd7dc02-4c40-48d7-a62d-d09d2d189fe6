'''
Created on Otc 16, 2021

@author: jon
'''
from django.conf.urls import url

from User import views

urlpatterns = [
    url(r'^lpapi/user/getProfile/$', views.GetUserInfo.as_view()),
    url(r'^lpapi/user/pushid/register/$', views.RegisterPushId.as_view()),

    # fo2
    url(r'^fastnet/users/profile/$', views.GetUserInfo.as_view()),
    url(r'^fastnet/users/pushid/update/$', views.RegisterPushId.as_view()),

]
