"""
Created on 2021/10/26

@author: jon
"""
import smtplib
from email.header import Header
from email.mime.text import MIMEText

from Lamp import settings
from Lamp.settings import logger


class UserUtil:

    @staticmethod
    def SendEmail(to, content):

        with smtplib.SMTP(host="smtp.gmail.com", port=587) as smtp:
            try:
                smtp.ehlo()
                smtp.starttls()
                smtp.login(settings.EMAIL_ACCOUNT, settings.EMAIL_PASSWORD)

                logger.info(f'send email to {to}, content={content}')

                msg = MIMEText(content, 'html', 'utf-8')
                msg['From'] = Header(settings.EMAIL_ACCOUNT, 'utf-8')
                msg['To'] = Header(to, 'utf-8')
                msg['Subject'] = Header('password reset code', 'utf-8')

                smtp.sendmail(settings.EMAIL_ACCOUNT, to, msg.as_string())

                logger.info(f"send email {to}, done")

            except Exception as e:
                logger.error(f"send email {to}, error: ", e)
