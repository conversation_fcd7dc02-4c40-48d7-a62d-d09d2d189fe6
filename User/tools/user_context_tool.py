import json

from Lamp.settings import logger
from Order.tools.tool_order import OrderTool
from User.models import UserTraffic
from User.tools.user_tool import UserTool
from User.tools.user_vip_tool import UserVipTool


class UserContextTool:
    @staticmethod
    def get_user_context_str(email: str, content: str, device_id: str) -> str:
        user_ctx_dict = UserContextTool.get_user_context_dict(email, content, device_id)
        user_ctx_str = json.dumps(user_ctx_dict, indent=4, default=str, ensure_ascii=False)
        return user_ctx_str

    @staticmethod
    def get_user_context_dict(email: str, content: str, device_id: str) -> dict:
        try:
            order = OrderTool.get_order_ignore_status(device_id)
            user = UserTool.get_user_by_device_id(device_id)
            ut = UserTraffic.objects.filter(device_id=device_id).first()
            if ut:
                user_traffic_cnt = ut.usage_cnt
            else:
                user_traffic_cnt = 0

            if not user:
                return {}
            user_context_dict = {
                "用户id": user.id,
                "用户设备": device_id,
                "订单original_transaction_id": order.original_transaction_id if order else '无订单',
                "订单创建时间": order.created_at if order else '无订单',
                "订单过期时间": order.expired_at if order else '无订单',
                "订单状态": order.order_status if order else '无订单',
                "订单是否有效": order.valid if order else '无订单',
                "订单过期意图": order.expiration_intent if order else '无订单',
                "用户赠送VIP天数": UserVipTool.get_send_vip_days(device_id),
                "订单撞车情况": f"{json.dumps(OrderTool.get_duplicate_info_list(device_id))}",
                "邮箱": email,
                "反馈内容": content,
                "创建时间": user.created_at,
                "流量cnt": user_traffic_cnt,
            }
            return user_context_dict
        except Exception:
            logger.error(f"[UserContextTool] failed for {device_id}, content: {content}", exc_info=True)
            return {}
