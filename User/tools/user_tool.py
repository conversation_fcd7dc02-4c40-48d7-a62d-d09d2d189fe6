from typing import Union

from Common.util import Util
from Lamp import settings
from Lamp.settings import logger
from User.models import User


class UserTool:

    @staticmethod
    def create_mock_number(device_id: str) -> str:
        """
        如果用户没有号码，我们给他根据 device_id mock一个
        :param device_id:
        :return:
        """
        user = UserTool.get_user_by_device_id(device_id)
        if not user:
            logger.warning(f"[UserTool.create_mock_number] device_id not exists: {device_id}")
            return ""

        s = str(user.id)
        for i in range(8 - len(s)):
            s = '0' + s
        return settings.APP_IT_SUPPORT_NO_NUMBER_PREFIX + s

    @staticmethod
    def get_device_id_from_mock_number(mock_phone: str) -> str:
        """
        从mock 的number抓出来 device_id
        :param mock_phone:
        :return:
        """
        mock_phone = mock_phone.replace(settings.APP_IT_SUPPORT_NO_NUMBER_PREFIX, "")

        if Util.has_alphanumeric_chars(mock_phone):
            device_id = mock_phone
            return device_id

        zero_index = 0
        for i in mock_phone:
            if i == '0':
                zero_index += 1
            else:
                break
        user_id_str = mock_phone[zero_index:]
        user = UserTool.get_user_by_id(int(user_id_str))
        return user.device_id

    @staticmethod
    def get_user_by_id(user_id: int) -> Union[None, User]:
        if not user_id:
            logger.error(f"UserTool.get_user_by_id user_id is not valid: {user_id}")
            return None
        user = User.objects.filter(id=user_id).first()
        if not user:
            return None
        return user

    @staticmethod
    def get_user_by_device_id(device_id: str) -> Union[None, User]:
        if not device_id:
            logger.error(f"UserTool.get_user_by_id device_id is not valid: {device_id}")
            return None
        user = User.objects.filter(device_id=device_id).first()
        if not user:
            return None
        return user

    @staticmethod
    def get_user_by_id_with_deleted(user_id: int) -> Union[None, User]:
        if not user_id:
            logger.error(f"[UserTool.get_user_by_id_with_deleted] user_id is not valid: {user_id}")
            return None
        user = User.objects.filter(id=user_id).first()
        if not user:
            return None
        return user

    @staticmethod
    def get_user_by_device_id_with_deleted(device_id: str) -> Union[None, User]:
        if not device_id:
            logger.error(f"UserTool.get_user_by_device_id_with_deleted device_id is not valid: {device_id}")
            return None
        user = User.objects.filter(device_id=device_id).first()
        if not user:
            return None
        return user

    @staticmethod
    def get_user_by_sip_username(sip_username: str) -> Union[None, User]:
        if not sip_username:
            logger.error(f"UserTool.get_user_by_sip_username sip_username is not valid: {sip_username}")
            return None
        user = User.objects.filter(sip_username=sip_username).first()
        if not user:
            return None
        return user

    @staticmethod
    def update_push_id(device_id: str, push_id: str):
        if not push_id or len(push_id) > 256:
            logger.error(f"[UserTool.update_push_id] device_id:{device_id}, push_id is invalid: {push_id}")

        User.objects.filter(device_id=device_id).update(jpush_id=push_id)

    @staticmethod
    def update_latest_sms_ts(device_id: str, latest_sms_ts: int):
        User.objects.filter(device_id=device_id).update(latest_sms_ts=latest_sms_ts)
