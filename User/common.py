"""
Created on 2021/10/10

@author: jon
"""
import time

from Common.util import Util
from Lamp.settings import logger
from Order.common import OrderCommon
from User.dao.user_dao import UserDao


class UserCommon:
    @staticmethod
    def CombineUserInfo(device_id: str) -> dict:
        is_expired, expired_at = OrderCommon.is_expired(device_id)

        j = {
            "device_id": device_id,
            "is_expired": is_expired,
            "expired_at": expired_at,
            "is_popup_user_terms": True,  # 用户条款
            "is_free": False,
            "free_expired_at": None,
            "ad_free_time": 30,  # 分钟
            "is_show_open_ad": False,  # 是否开屏广告
            "is_show_reward_ad_vip": False,  # 是否激励广告
            "is_show_reward_ad_home": False,  # 是否激励广告：首页
            "is_connect_success_before": False,  # 是否连接成功过，UserTraffic的单位是MB
        }

        # 是否存在流量，大于500M，我们才评估需要弹窗去求好评
        ut = UserDao.get_user_traffic(device_id)
        if ut and ut.usage_cnt > 500:
            j["is_connect_success_before"] = True

        logger.info(f"[CombineUserInfo] device_id:{device_id}, info: {j}")
        return j

    @staticmethod
    def GenDeviceToken(device_id: str) -> str:
        return Util.MD5Sum(device_id + str(time.time()))
