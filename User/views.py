from Common.err import ErrInfo
from Common.views import CommonView
from Lamp.settings import logger
from User.common import UserCommon
from User.models import User
from User.tools.user_tool import UserTool


class GetUserInfo(CommonView):

    # POST /user/info/
    @CommonView.VerifySign
    def post(self, request):
        headers = self.GetHeaderInRequest(request)
        # logger.info(f"[GetUserInfo] device_id: {headers['device_id']}")

        device_id = headers['device_id']
        user = User.objects.filter(device_id=device_id)
        if not user:
            try:
                user = User(device_id=device_id,
                            jpush_id=headers['jpush_id'],
                            appid=headers['appid'])
                # insert db
                user.save()
            except Exception as ex:
                if "Duplicate entry" in str(ex):
                    pass
                else:
                    logger.error(f"[GetUserInfo] device_id: {device_id} failed", exc_info=True)

        res = UserCommon.CombineUserInfo(device_id)
        logger.info(f"[GetUserInfo] device_id: {device_id}, user info:{res}")
        return self.ReturnSuccess(res)


class RegisterPushId(CommonView):

    @CommonView.VerifySign
    def post(self, request):
        try:
            data, err_code, err_msg = self.ReadPostJson(request, ("push_id",))
            if err_code != 0:
                logger.error(f"[User.views.RegisterPushId] param error:  {request.body}")
                return self.ReturnError(err_code, err_msg)

            header = self.GetHeaderInRequest(request)
            device_id = header['device_id']
            push_id = data['push_id']
            UserTool.update_push_id(device_id, push_id)
            logger.info(f"[User.view.RegisterPushId] user: {device_id}, push_id: {push_id}, register success")
            return self.ReturnSuccess({})

        except Exception:
            logger.error(f"[User.views.RegisterPushId] check toNumber error, body: {request.body}", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)
