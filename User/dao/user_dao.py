from Lamp.settings import logger
from User.models import UserTraffic


class UserDao:
    @staticmethod
    def is_hit_traffic_limit(device_id: str) -> bool:
        user_traffic = UserDao.get_user_traffic(device_id)
        limit_cnt = 1000 * 300  # 300GB

        # todo: delete after 2025-10-10
        if device_id == "ea64b665-3a46-4052-a1e1-176131bcb17e":
            limit_cnt += 1000 * 100

        if user_traffic and user_traffic.usage_cnt > limit_cnt:
            logger.error(
                f"[is_hit_traffic_limit] user: {device_id} hit traffic limit, cnt:{user_traffic.usage_cnt} MB, "
                f"limit: 300GB")
            return True
        return False

    @staticmethod
    def get_user_traffic(device_id: str):
        return UserTraffic.objects.filter(device_id=device_id).first()
