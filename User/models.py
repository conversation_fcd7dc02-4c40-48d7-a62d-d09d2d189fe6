"""
Created on 2021/10/9

@author: jon
"""

from django.contrib import admin
from django.db import models


class User<PERSON>anager(models.Manager):
    pass


# save idfa only when user signup, since indicates user source
class User(models.Model):
    email = models.Char<PERSON>ield('email', max_length=50)
    login_id = models.CharField('login_id', max_length=256)  # 各个第三方的登录唯一 id
    login_type = models.IntegerField('login_type', default=0)  # 1 google 2 apple 3 facebook

    idfa = models.CharField('idfa', max_length=200, blank=True)
    device_id = models.CharField('device_id', max_length=128, blank=True)
    token = models.CharField('token', max_length=128, null=True)
    jpush_id = models.CharField('jpush_id', max_length=256, blank=True)
    expired_at = models.DateTimeField('expired_at', null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTime<PERSON>ield(auto_now=True)
    appid = models.IntegerField('appid', blank=True, default=0)
    deleted = models.IntegerField('deleted', blank=True, default=0)
    ray_id = models.CharField('ray_id', max_length=128, blank=True)
    # 用户最新的拉取短信打点
    latest_sms_ts = models.IntegerField('latest_sms_ts', blank=True, default=0)
    # 用户使用的流量cnt
    usage_cnt = models.IntegerField('usage_cnt', blank=True, default=0)

    objects = UserManager()


class UserUsage(models.Model):
    device_id = models.CharField('device_id', max_length=128)  # 各个第三方的登录唯一 id
    country = models.CharField('country', max_length=50)
    ip = models.CharField('ip', max_length=50)
    account_id = models.CharField('account_id', max_length=50)
    password = models.CharField('password', max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    ray_id = models.CharField('ray_id', max_length=128, blank=True)
    objects = UserManager()


class UsageRecord(models.Model):
    device_id = models.CharField('device_id', max_length=128)  # 各个第三方的登录唯一 id
    req_country = models.CharField('req_country', max_length=8, blank=True)
    country = models.CharField('country', max_length=8)
    ip = models.CharField('ip', max_length=20)
    account_id = models.CharField('account_id', max_length=20)
    password = models.CharField('password', max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    ray_id = models.CharField('ray_id', max_length=128, blank=True)
    net_type = models.CharField('net_type', max_length=16, blank=True)
    node_group_name = models.CharField('node_group_name', max_length=128, blank=True)
    objects = UserManager()


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    files = ('device_id', 'ray_id', 'created_at', 'updated_at',)
    list_display = ('device_id', 'ray_id', 'created_at', 'updated_at',)
    list_display_link = ('id',)
    search_fields = ('device_id', 'ray_id',)
    list_per_page = 50
    objects = UserManager()


@admin.register(UserUsage)
class UserUsageAdmin(admin.ModelAdmin):
    files = ('device_id', 'country', 'ip', 'created_at', 'updated_at')
    list_display = ('device_id', 'country', 'ip', 'created_at', 'updated_at')
    list_display_link = ('id',)
    list_filter = ('country',)
    search_fields = ('device_id',)
    list_per_page = 50
    objects = UserManager()


@admin.register(UsageRecord)
class UserUsageRecordAdmin(admin.ModelAdmin):
    files = ('device_id', 'ray_id', "ip", 'req_country', 'country', 'created_at', 'updated_at')
    list_display = ('device_id', 'ray_id', "ip", 'req_country', 'country', 'created_at', 'updated_at')
    list_display_link = ('id',)
    search_fields = ('device_id', 'ray_id',)
    list_per_page = 50
    objects = UserManager()


class UserTraffic(models.Model):
    device_id = models.CharField('device_id', max_length=128)  # 各个第三方的登录唯一 id
    ray_id = models.CharField('ray_id', max_length=128)  # 各个第三方的登录唯一 id
    usage_cnt = models.IntegerField('usage_cnt', blank=True, default=0)  # 单位：M
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    objects = UserManager()


@admin.register(UserTraffic)
class UserTrafficAdmin(admin.ModelAdmin):
    files = ('device_id', 'ray_id', 'usage_cnt', 'created_at', 'updated_at')
    list_display = ('device_id', 'ray_id', 'usage_cnt', 'created_at', 'updated_at')
    search_fields = ('device_id', 'ray_id',)
    list_per_page = 50
    objects = UserManager()
