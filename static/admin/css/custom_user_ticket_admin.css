.thumbnail {
    transition: all 0.3s ease; /* 添加平滑过渡效果 */
    border: 1px solid #ddd;    /* 添加边框便于识别 */
    margin: 2px;
}

.thumbnail:hover {
    border-color: #888; /* 悬停效果 */
}

#modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: none; /* 初始时隐藏 */
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

#modal-content {
    position: relative;
    max-width: 80%;  /* 最大宽度为屏幕宽度的 80% */
    max-height: 80%; /* 最大高度为屏幕高度的 80% */
    overflow: hidden;
}

#modal-image {
    width: 100%;  /* 使图片宽度自适应容器 */
    height: auto;  /* 高度自动调整以保持宽高比 */
    max-width: 600px;  /* 最大宽度限制为 600px */
    max-height: 600px; /* 最大高度限制为 600px */
    object-fit: contain;  /* 保持图片的宽高比，避免拉伸 */
}
