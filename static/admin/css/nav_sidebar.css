/* 基础容器样式 - 兼容两个版本的侧边栏容器 */
#nav-sidebar,
div[class*="admin-sidebar"] {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 240px;
    background-color: #2c3e50;
    color: #ecf0f1;
    padding-top: 50px; /* 适配顶部导航栏高度 */
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    transition: transform 0.3s ease, width 0.3s ease;
    z-index: 1000;
    box-sizing: border-box; /* 确保 padding 不影响总宽度 */
}

/* 侧边栏标题样式 - 兼容不同层级的标题结构 */
#nav-sidebar .sidebar-header,
#nav-sidebar > h1,
[class*="admin-sidebar"] .sidebar-header {
    padding: 0 15px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin: 0 0 15px 0;
}

#nav-sidebar .sidebar-header h1,
#nav-sidebar > h1,
[class*="admin-sidebar"] .sidebar-header h1 {
    font-size: 1.2rem;
    margin: 0;
    font-weight: 600;
}

/* 导航列表基础样式 - 适配不同版本的列表结构 */
#nav-sidebar ul,
[class*="admin-sidebar"] ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#nav-sidebar li,
[class*="admin-sidebar"] li {
    margin: 0;
    position: relative;
}

/* 导航链接样式 - 兼容两个版本的链接选择器 */
#nav-sidebar a,
#nav-sidebar .menu-item,
[class*="admin-sidebar"] a,
[class*="admin-sidebar"] .menu-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.95rem;
    box-sizing: border-box;
}

#nav-sidebar a:hover,
#nav-sidebar a.active,
#nav-sidebar .menu-item:hover,
#nav-sidebar .menu-item.active,
[class*="admin-sidebar"] a:hover,
[class*="admin-sidebar"] a.active,
[class*="admin-sidebar"] .menu-item:hover,
[class*="admin-sidebar"] .menu-item.active {
    background-color: #34495e;
    color: #ffffff;
    padding-left: 20px;
}

/* 图标样式 - 兼容可能的图标位置变化 */
#nav-sidebar a i,
#nav-sidebar .menu-item i,
[class*="admin-sidebar"] a i,
[class*="admin-sidebar"] .menu-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    font-size: 1rem;
}

/* 子菜单样式 - 适配不同版本的子菜单嵌套 */
#nav-sidebar .submenu,
#nav-sidebar ul ul,
[class*="admin-sidebar"] .submenu,
[class*="admin-sidebar"] ul ul {
    background-color: #1a252f;
    padding-left: 20px;
}

#nav-sidebar .submenu a,
#nav-sidebar ul ul a,
[class*="admin-sidebar"] .submenu a,
[class*="admin-sidebar"] ul ul a {
    padding-left: 30px;
    font-size: 0.9rem;
}

/* 折叠按钮样式 - 兼容两个版本的交互逻辑 */
#nav-sidebar .collapse-btn,
[class*="admin-sidebar"] .collapse-btn {
    position: absolute;
    top: 15px;
    right: -10px;
    width: 20px;
    height: 20px;
    background-color: #2c3e50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    border: none;
    color: #ecf0f1;
    z-index: 10;
}

#nav-sidebar .collapse-btn i,
[class*="admin-sidebar"] .collapse-btn i {
    font-size: 0.8rem;
}

/* 响应式适配 - 通用媒体查询替代版本专属类 */
@media (max-width: 768px) {
    #nav-sidebar,
    [class*="admin-sidebar"] {
        transform: translateX(-100%);
    }

    #nav-sidebar.expanded,
    [class*="admin-sidebar"].expanded {
        transform: translateX(0);
    }

    #nav-sidebar .sidebar-header,
    [class*="admin-sidebar"] .sidebar-header {
        padding-right: 30px;
    }
}

/* 滚动条样式 - 统一两个版本的滚动条显示 */
#nav-sidebar::-webkit-scrollbar,
[class*="admin-sidebar"]::-webkit-scrollbar {
    width: 6px;
}

#nav-sidebar::-webkit-scrollbar-track,
[class*="admin-sidebar"]::-webkit-scrollbar-track {
    background: #2c3e50;
}

#nav-sidebar::-webkit-scrollbar-thumb,
[class*="admin-sidebar"]::-webkit-scrollbar-thumb {
    background: #34495e;
    border-radius: 3px;
}

#nav-sidebar::-webkit-scrollbar-thumb:hover,
[class*="admin-sidebar"]::-webkit-scrollbar-thumb:hover {
    background: #4a69bd;
}

/* 兼容 Django 3.2 新增的侧边栏折叠状态 */
#nav-sidebar.collapsed,
[class*="admin-sidebar"].collapsed {
    width: 60px;
}

#nav-sidebar.collapsed a span,
#nav-sidebar.collapsed .sidebar-header,
[class*="admin-sidebar"].collapsed a span,
[class*="admin-sidebar"].collapsed .sidebar-header {
    display: none;
}

#nav-sidebar.collapsed a,
[class*="admin-sidebar"].collapsed a {
    justify-content: center;
    padding: 12px 0;
}

#nav-sidebar.collapsed a i,
[class*="admin-sidebar"].collapsed a i {
    margin-right: 0;
    font-size: 1.2rem;
}
