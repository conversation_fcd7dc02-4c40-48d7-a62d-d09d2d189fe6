<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit your ticket</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }

        .form-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .form-container label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-container input[type="text"],
        .form-container input[type="email"],
        .form-container textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 15px;
        }

        .form-container input[type="submit"], .form-container button {
            background-color: #4CAF50; /* Green */
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }

        .form-container input[type="submit"]:hover, .form-container button:hover {
            background-color: #45a049; /* Darker green */
        }

        .form-container select {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            resize: vertical; /* only textarea */
        }

        .form-container select {
            -moz-appearance: none; /* Firefox */
            -webkit-appearance: none; /* Safari 和 Chrome */
            appearance: none;
            background-image: linear-gradient(45deg, transparent 50%, rgb(128, 128, 128) 50%),
            linear-gradient(135deg, rgb(128, 128, 128) 50%, transparent 50%),
            linear-gradient(to right, #ccc, #ccc);
            background-position: calc(100% - 20px) calc(1em + 2px),
            calc(100% - 15px) calc(1em + 2px),
            calc(100% - 2.5em) 0.5em;
            background-size: 5px 5px,
            5px 5px,
            1px 1.5em;
            background-repeat: no-repeat;
        }

        .required-field:after {
            content: " *";
            color: red;
            margin-left: 5px;
        }

        input[type="text"],
        input[type="email"],
        input[type="reason"] {
            width: 200px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        #success-message {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
            background-color: #f2f2f2;
            font-family: Arial, sans-serif;
            font-size: 18px;
            color: #333;
            border: 2px solid #4CAF50;
            padding: 20px;
            border-radius: 10px;
        }

        #success-message h2 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        #success-message p {
            margin-bottom: 5px;
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // set wxid
            let queryString = window.location.search;
            let urlParams = new URLSearchParams(queryString);
            let wxid = urlParams.get('wxid');
            let wxidInput = document.getElementById('wxid-input');
            if (wxidInput) {
                wxidInput.value = wxid;
            }

            // history
            let selectElement = document.getElementById('ticket-select');
            let savedOption = localStorage.getItem('selected-ticket-type');
            if (savedOption) {
                selectElement.value = savedOption;
            }
            selectElement.addEventListener('change', function () {
                localStorage.setItem('selected-ticket-type', this.value);
            });

            let usernameElement = document.getElementById('username');
            let savedUsername = localStorage.getItem('username');
            if (savedUsername) {
                usernameElement.value = savedUsername;
            }
            usernameElement.addEventListener('blur', function () {
                localStorage.setItem('username', this.value);
            });

            let emailElement = document.getElementById('email');
            let savedEmail = localStorage.getItem('email');
            if (savedEmail) {
                emailElement.value = savedEmail;
            }
            emailElement.addEventListener('blur', function () {
                localStorage.setItem('email', this.value);
            });

            let ticketElement = document.getElementById('ticket');
            let savedTicket = localStorage.getItem('ticket');
            if (savedTicket) {
                ticketElement.value = savedTicket;
            }
            ticketElement.addEventListener('blur', function () {
                localStorage.setItem('ticket', this.value);
            });
        });


        function submitForm() {
            // Disable the submit button and change its color
            const submitButton = document.querySelector('.form-container button');
            submitButton.disabled = true;
            submitButton.style.backgroundColor = '#ccc';
            submitButton.style.cursor = 'not-allowed';

            // Create a new FormData object
            let formData = new FormData();

            // Get the values from the form fields
            const ticketType = document.getElementById('ticket-select').value.trim();
            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();
            const ticket = document.getElementById('ticket').value.trim();
            const wxid = document.getElementById('wxid-input').value.trim();

            if (ticketType.length === 0) {
                alert('ticket type cannot be empty!');
                return;
            }

            if (ticketType.length > 64) {
                alert('ticket type invalid!');
                return;
            }

            if (username.length === 0) {
                alert('username cannot be empty!');
                return;
            }
            if (username.length > 128) {
                alert('username invalid, too long!');
                return;
            }

            if (email.length === 0) {
                alert('email cannot be empty!');
                return;
            }
            if (email.length > 128) {
                alert('email invalid, too long!');
                return;
            }

            // check email format
            let emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Invalid email address!');
                return;
            }
            if (ticket.length === 0) {
                alert('ticket cannot be empty!');
                return;
            }
            if (ticket.length > 2048) {
                alert('ticket content invalid, too long!');
                return;
            }

            if (wxid.length === 0) {
                alert('invalid source');
                return;
            } else if (wxid.length > 64) {
                alert('invalid source');
                return;
            }

            // Add the form field values to the FormData object
            formData.append('ticket_type', ticketType);
            formData.append('username', username);
            formData.append('email', email);
            formData.append('ticket', ticket);
            formData.append('wxid', wxid);

            // Add each image file to the FormData object
            let imageFiles = document.getElementById('image').files;
            for (let i = 0; i < imageFiles.length; i++) {
                formData.append('images', imageFiles[i]);
            }

            // Make a POST request to the backend using fetch API
            fetch('https://api.gongbaojiding.xyz/lpapi/ticket/submit/', {
                method: 'POST',
                body: formData
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log(data);
                    if (data.err_code !== 0) {
                        alert("submit failed: " + data.err_msg);
                        console.log(data)
                    } else {
                        showSuccessView();
                    }
                })
                .catch(error => {
                    console.error('There has been a problem with your fetch operation:', error);
                })
                .finally(() => {
                    // Enable the submit button and restore its original color after 3 seconds
                    setTimeout(() => {
                        submitButton.disabled = false;
                        submitButton.style.backgroundColor = '#4CAF50';
                        submitButton.style.cursor = 'pointer';
                    }, 3000);
                });
        }

        function showSuccessView() {
            document.getElementById('form-container-id').style.display = 'none';
            document.getElementById('success-message').style.display = 'block';
        }
    </script>
</head>
<body>

<div class="form-container" id="form-container-id">

    <label for="ticket-select" class="required-field">Ticket type:</label>
    <select id="ticket-select" name="reason" required>
        <option value="">Select a ticket type</option>
        <option value="connected_issue">Connection ticket</option>
        <option value="refund">Refund request</option>
        <option value="subscription_issue">Subscription ticket</option>
    </select>

    <label for="username">Name:</label>
    <input type="text" id="username" name="username">

    <label for="email">Email:</label>
    <input type="email" id="email" name="email">

    <label for="ticket" class="required-field">Describe your problem:</label>
    <textarea id="ticket" name="ticket" rows="4" required></textarea>


    <!-- hidden wxid -->
    <input type="hidden" id="wxid-input" name="wxid">

    <!-- Add a file input field for image upload -->
    <label for="image">Upload Image:</label>
    <input type="file" id="image" name="image" accept="image/*" multiple>

    <button onclick="submitForm()">Submit your ticket</button>
</div>

<div id="success-message" style="display: none;">
    <h2>Submit Successfully!</h2>
    <p>Your ticket has been successfully submitted.</p>
    <p>Thank you for your feedback!</p>
    <p>We will process your ticket within 24-48 hours. Please be patient as we work on it.</p>
</div>

</body>
</html>
