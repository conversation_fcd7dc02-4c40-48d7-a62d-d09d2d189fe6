from django.db import models


class Tool(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    objects = models.Manager()


class UpdateSubscriptionTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Subscription - Update User Subscription'
        verbose_name_plural = 'Subscription - Update User Subscription'


class GetUserSubscriptionByDeviceIdTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Subscription - Get User Subscription by DeviceId'
        verbose_name_plural = 'Subscription - Get User Subscription by DeviceId'


class GetUserSubscriptionByCertTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Subscription - Get User Subscription by Certificate'
        verbose_name_plural = 'Subscription - Get User Subscription by Certificate'
