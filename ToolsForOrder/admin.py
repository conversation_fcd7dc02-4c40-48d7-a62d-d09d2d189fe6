from django.contrib import admin
from django.http import JsonResponse
from django.template.response import TemplateResponse
from django.urls import path

from Lamp.settings import logger
from Order.tools.tool_apple_order import OrderAppleTool
from Order.tools.tool_order import OrderTool
from Order.tools.tool_refresh_order import Refresh<PERSON>rderTool
from ToolsForOrder.models import UpdateSubscriptionTool, GetUserSubscriptionByCertTool, \
    GetUserSubscriptionByDeviceIdTool
from User.tools.user_tool import UserTool


@admin.register(UpdateSubscriptionTool)
class UpdateSubscriptionToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/update_subscription.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('update_subscription/', self.admin_site.admin_view(self.update_subscription),
                 name='update_subscription'),
        ]
        return custom_urls + urls

    @staticmethod
    def update_subscription(request):
        if request.method == 'POST':
            device_id = request.POST.get('deviceid')

            logger.info(f"[UpdateSubscriptionToolAdmin] device_id:{device_id}")
            ret = RefreshOrderTool.refresh_user_vip(device_id)
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/update_subscription.html', {})


@admin.register(GetUserSubscriptionByDeviceIdTool)
class GetUserSubscriptionByUserIdToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/get_user_subscription_by_deviceid.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('get_user_subscription_by_deviceid/',
                 self.admin_site.admin_view(self.get_user_subscription_by_deviceid),
                 name='get_user_subscription_by_deviceid'),
        ]
        return custom_urls + urls

    @staticmethod
    def get_user_subscription_by_deviceid(request):
        if request.method == 'POST':
            data = request.POST

            device_id = data['deviceid'].strip("\n").strip()
            if not UserTool.get_user_by_device_id(device_id):
                ret = {"err": f'device_id: {device_id} not exists'}
                return JsonResponse(ret)

            order = OrderTool.get_user_order_without_condition(device_id)
            if not order:
                ret = {"err": f"device_id:{device_id} order is not exist"}
                return JsonResponse(ret)

            res, is_sandbox = OrderAppleTool.get_cert_rsp_from_apple(order.certificate)
            ret = {"apple_res": res, "is_sandbox": is_sandbox}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/get_user_subscription_by_deviceid.html', {})


@admin.register(GetUserSubscriptionByCertTool)
class GetUserSubscriptionByCertToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/get_user_subscription_by_cert.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('get_user_subscription_by_cert/', self.admin_site.admin_view(self.get_user_subscription_by_cert),
                 name='get_user_subscription_by_cert'),
        ]
        return custom_urls + urls

    @staticmethod
    def get_user_subscription_by_cert(request):
        if request.method == 'POST':
            data = request.POST
            cert = data['cert']
            res, is_sandbox = OrderAppleTool.get_cert_rsp_from_apple(cert)
            ret = {"apple_res": res, "is_sandbox": is_sandbox}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/get_user_subscription_by_cert.html', {})
