CREATE TABLE `Report_finance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `day` varchar(50) NOT NULL,
  `total_income` float NOT NULL DEFAULT '0',
  `total_vip_income` float NOT NULL DEFAULT '0',
  `total_vip_week_income` float NOT NULL DEFAULT '0',
  `total_vip_month_income` float NOT NULL DEFAULT '0',
  `total_vip_season_income` float NOT NULL DEFAULT '0',
  `total_vip_year_income` float NOT NULL DEFAULT '0',
  `today_total_income` float NOT NULL DEFAULT '0',
  `today_vip_income` float NOT NULL DEFAULT '0',
  `today_vip_year_income` float NOT NULL DEFAULT '0',
  `today_vip_season_income` float NOT NULL DEFAULT '0',
  `today_vip_month_income` float NOT NULL DEFAULT '0',
  `today_vip_week_income` float NOT NULL DEFAULT '0',
  `total_user_cnt` int(11) NOT NULL DEFAULT '0',
  `total_vip_user_cnt` int(11) NOT NULL DEFAULT '0',
  `total_vip_week_user_cnt` int(11) NOT NULL DEFAULT '0',
  `total_vip_month_user_cnt` int(11) NOT NULL DEFAULT '0',
  `total_vip_season_user_cnt` int(11) NOT NULL DEFAULT '0',
  `total_vip_year_user_cnt` int(11) NOT NULL DEFAULT '0',
  `total_baipiao_user_cnt` int(11) NOT NULL DEFAULT '0',
  `total_refund_user_cnt` int(11) NOT NULL DEFAULT '0',
  `today_vip_user_cnt` int(11) NOT NULL DEFAULT '0',
  `today_vip_week_user_cnt` int(11) NOT NULL DEFAULT '0',
  `today_vip_month_user_cnt` int(11) NOT NULL DEFAULT '0',
  `today_vip_season_user_cnt` int(11) NOT NULL DEFAULT '0',
  `today_vip_year_user_cnt` int(11) NOT NULL DEFAULT '0',
  `appid` tinyint(4) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_day` (`day`,`appid`) USING BTREE,
  KEY `idx_day` (`day`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;