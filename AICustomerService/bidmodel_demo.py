import json

import requests
from requests.adapters import HTT<PERSON>dapter, Retry

from Lamp.settings import logger
# import logging
# logger = logging.getLogger(__name__)

# 创建一个Session对象，它会隐式地使用连接池
session = requests.Session()

# 创建一个Retry对象，配置重试策略
retry_strategy = Retry(
    total=3,  # 最大重试次数
    backoff_factor=1,  # 重试间隔时间的回退因子
    status_forcelist=[500, 502, 503, 504],  # 针对这些 HTTP 状态码进行重试
)

# 设置连接池的大小（例如，最大同时打开的连接数）
# 注意：这个值需要根据你的具体需求和服务器的能力来设置
adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=10, pool_maxsize=20)
session.mount('http://', adapter)
session.mount('https://', adapter)

ZHIPU_APP_ID = '1965367920364310528'
ZHIPU_TOKEN = 'fe0e70cea0e944209d8aa8245d6b751e.sF6UqVQPLJWtTc3q'

url_new_chat = 'https://open.bigmodel.cn/api/llm-application/open//v2/application/%s/conversation' % ZHIPU_APP_ID
url_send_question = 'https://open.bigmodel.cn/api/llm-application/open/v2/application/generate_request_id'
headers = {'Authorization': 'Bearer %s' % ZHIPU_TOKEN}


def zhipu_chat(question):
    # create chat, can each user has his own chat, better for multi-round chat
    r = session.post(url_new_chat, headers=headers, json={})
    logger.info('[ZHIPU]create chat:', r.json())
    chat_id = r.json()['data']['conversation_id']

    # send question
    data = {
        "app_id": ZHIPU_APP_ID,
        "conversation_id": chat_id,
        "key_value_pairs": [
            {"id": "user", "name": "用户提问", "type": "input", "value": question}
        ]
    }

    r = session.post(url_send_question, headers=headers, json=data)
    logger.info(f'[ZHIPU] send question:{question}, json:{r.json()}')
    answer_id = r.json()['data']['id']

    # get answer
    url_get_answer = 'https://open.bigmodel.cn/api/llm-application/open/v2/model-api/%s/sse-invoke' % answer_id

    with session.post(url_get_answer, headers=headers, stream=True, timeout=30) as resp:
        try:
            for line in resp.iter_lines():
                line = line.decode('utf8')
                if line.find('block_type":"output"') > 0 and line.find('out_content') > 0:
                    line = line.replace('data:', '')
                    res = json.loads(line)
                    answer = res['extra_input']['block_data']['out_put']['out_content']
                    logger.info(f'[ZHIPU] question: [{question}], answer: [{answer}]')
                    return answer
        except Exception as e:
            logger.error(f"[ZHIPU] get answer error", exc_info=True)
            return None


if __name__ == '__main__':

    contents = [
        # "change number",
        # "delete account",
        # "why can not receive message?",
        # "fuck you scam app!",
        "vpn not work"
    ]
    for c in contents:
        print(zhipu_chat(c))
