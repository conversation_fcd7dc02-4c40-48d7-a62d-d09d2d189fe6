from AICustomerService.bidmodel_demo import zhipu_chat
from Lamp.settings import logger


class AiCustomerServiceUtil:
    @staticmethod
    def get_ai_answer(query: str) -> str:
        try:
            logger.info(f"[AiCustomerServiceUtil] start handle: {query}")
            query = query.strip()
            query = query.replace("\n", " ")
            if not query:
                logger.error(f"[AiCustomerServiceUtil] q is empty")
                return ""

            answer = zhipu_chat(query)
            if not answer:
                logger.error(f"[AiCustomerServiceUtil] q:{query}, answer is none!")
                return ""

            logger.info(f"[AiCustomerServiceUtil] q:{query}, a:{answer}")
            return answer
        except Exception:
            query_ = query.replace("\n", " ")
            logger.error(f"[AiCustomerServiceUtil] q:{query_},failed", exc_info=True)
            return ""


if __name__ == '__main__':
    answer = AiCustomerServiceUtil.get_ai_answer("change number")
    print(answer)
