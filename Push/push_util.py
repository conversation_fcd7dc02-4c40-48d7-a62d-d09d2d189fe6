import firebase_admin
import firebase_admin.exceptions
from django.conf import settings
from django.core.cache import cache
from firebase_admin import _messaging_utils
from firebase_admin._messaging_utils import APNSPayload, Aps
from firebase_admin.messaging import Message

from Common.rediskey import RedisKey
from Lamp.settings import logger
from Sms.tools.tool_sms import SmsTool
from User.tools.user_tool import UserTool


class PushUtil(object):

    @staticmethod
    def calc_push_badge(device_id: str):
        latest_ts = cache.get(RedisKey.GenSmsLatestTimestamp(device_id))
        if not latest_ts:
            badge = 1
        else:
            # 否则只拉用户的比latest_ts大的收短信
            no_read_sms_cnt = SmsTool.get_no_read_cnt(device_id, latest_ts)
            badge = no_read_sms_cnt + 1

        return badge

    @staticmethod
    def notify_by_user(device_id: str, content: str):
        user = UserTool.get_user_by_device_id(device_id)
        if not user:
            logger.warning(f"[PushUtil.notify_by_user] device_id:{device_id} not exists")
            return

        if not user.jpush_id:
            logger.warning(f"[PushUtil.notify_by_user] device_id:{device_id} has no push_id")
            return

        badge = PushUtil.calc_push_badge(device_id)
        PushUtil.notify_by_fcm(device_id=device_id, push_id=user.jpush_id, content=content, badge=badge)

    @staticmethod
    def notify_by_fcm(device_id: str, push_id: str, content: str, badge: int):
        """
        https://developer.apple.com/library/archive/documentation/NetworkingInternet/Conceptual/RemoteNotificationsPG/PayloadKeyReference.html#//apple_ref/doc/uid/TP40008194-CH17-SW1

        :param device_id:
        :param push_id:
        :param content:
        :param badge:
        :return:
        """
        try:
            if len(push_id) < 30:
                logger.warning(
                    f"[PushUtil.notify_by_fcm] device_id:{device_id} push_id invalid: {push_id}, content: {content}")
                return

            message = firebase_admin.messaging.Message(
                notification=firebase_admin.messaging.Notification(
                    title=settings.FCM_APP_NAME,
                    body=content[0:256] if content else "Receive an image",
                    image=None,
                ),
                apns=_messaging_utils.APNSConfig(payload=APNSPayload(aps=Aps(badge=badge, sound="default"))),
                token=push_id,
            )
            fcm_app = firebase_admin.get_app(settings.FCM_APP_NAME)
            result = firebase_admin.messaging.send(message, app=fcm_app)
            logger.info(
                f"[PushUtil.notify_by_fcm] device_id:{device_id}, _notify_by_fcm, badge:{badge}, result: {result}")
        except firebase_admin._messaging_utils.UnregisteredError:
            logger.warning(f"[PushUtil.notify_by_fcm] device_id:{device_id} Unregistered,  content: {content}, failed")
        except firebase_admin.exceptions.NotFoundError:
            logger.warning(f"[PushUtil.notify_by_fcm] device_id:{device_id} NotFoundError, content: {content}, failed")
        except Exception:
            logger.error(f"[PushUtil.notify_by_fcm] device_id:{device_id} _notify_by_fcm, content: {content}, failed",
                         exc_info=True)

    @staticmethod
    def batch_notify_by_fcm(push_ids: list, content: str, max_batch_size: int = 500):
        """
        批量发送推送通知，支持最多500个push_id

        :param push_ids: 推送ID列表（FCM令牌）
        :param content: 推送内容（所有设备共享相同内容）
        :param max_batch_size: 每批最大数量，默认500（Firebase限制）
        :return: 推送结果统计 dict
        """
        if not push_ids:
            logger.warning("[PushUtil.batch_notify_by_fcm] push_ids list is empty")
            return {"total": 0, "success": 0, "failed": 0, "invalid": 0, "errors": []}

        # 准备统计结果
        result_stats = {
            "total": len(push_ids),
            "success": 0,
            "failed": 0,
            "invalid": 0,
            "errors": []
        }

        try:
            # 按批次处理
            batches = [push_ids[i:i + max_batch_size] for i in range(0, len(push_ids), max_batch_size)]
            # 修复：使用默认应用或正确的应用名称
            try:
                # 尝试获取指定应用
                fcm_app = firebase_admin.get_app(settings.FCM_APP_NAME)
            except ValueError:
                # 如果指定应用不存在，使用默认应用
                logger.error(
                    f"[PushUtil.batch_notify_by_fcm] FCM app {settings.FCM_APP_NAME} not found, using default app")
                fcm_app = firebase_admin.get_app()

            for batch_index, batch in enumerate(batches):
                logger.info(f"[PushUtil.batch_notify_by_fcm] current batch:{batch_index}, batch size: {len(batch)}")
                # 创建批量消息
                batch_messages = []
                for idx, push_id in enumerate(batch):
                    message = Message(
                        notification=firebase_admin.messaging.Notification(
                            title=settings.FCM_APP_NAME,
                            body=content[0:256] if content else "Receive an image",
                            image=None,
                        ),
                        apns=_messaging_utils.APNSConfig(
                            payload=APNSPayload(aps=Aps(badge=1, sound="default"))
                        ),
                        token=push_id,
                    )
                    batch_messages.append(message)

                # 发送批量消息
                response = firebase_admin.messaging.send_each(batch_messages, app=fcm_app)

                # 处理批次结果
                for i, resp in enumerate(response.responses):
                    push_id = batch[i]
                    if resp.success:
                        result_stats["success"] += 1
                    else:
                        result_stats["failed"] += 1
                        error_msg = f"push_id:{push_id[:10]}... failed: {resp.exception}"
                        result_stats["errors"].append(error_msg)
                        logger.error(error_msg)

        except Exception as e:
            logger.error(f"[PushUtil.batch_notify_by_fcm] batch process failed: {str(e)}", exc_info=True)
            result_stats["errors"].append(f"Batch processing error: {str(e)}")

        logger.error(f"[PushUtil.batch_notify_by_fcm] batch process done, result_stats: {result_stats}")
        return result_stats

    @staticmethod
    def batch_notify_by_fcm_test(content: str, max_batch_size: int = 500):
        push_ids = [
            "dIQz8gP6uU07mgdQv57EUl:APA91bGoXR90jp0zh6aUH472i3JIMLJBRhVCiclwp2V6EYFGmskQviju3383QHK9o0-XW8cjcv8Tr8_5gULEekSHIur1JRcZGOsSo9X2RDMY2BVK00O8QN0"]
        if not push_ids:
            logger.warning("[PushUtil.batch_notify_by_fcm] push_ids list is empty")
            return {"total": 0, "success": 0, "failed": 0, "invalid": 0, "errors": []}

        # 准备统计结果
        result_stats = {
            "total": len(push_ids),
            "success": 0,
            "failed": 0,
            "invalid": 0,
            "errors": []
        }

        try:
            # 按批次处理
            batches = [push_ids[i:i + max_batch_size] for i in range(0, len(push_ids), max_batch_size)]
            # 修复：使用默认应用或正确的应用名称
            try:
                # 尝试获取指定应用
                fcm_app = firebase_admin.get_app(settings.FCM_APP_NAME)
            except ValueError:
                # 如果指定应用不存在，使用默认应用
                logger.error(
                    f"[PushUtil.batch_notify_by_fcm] FCM app {settings.FCM_APP_NAME} not found, using default app")
                fcm_app = firebase_admin.get_app()

            for batch_index, batch in enumerate(batches):
                logger.info(f"[PushUtil.batch_notify_by_fcm] current batch:{batch_index}, batch size: {len(batch)}")
                # 创建批量消息
                batch_messages = []
                for idx, push_id in enumerate(batch):
                    message = Message(
                        notification=firebase_admin.messaging.Notification(
                            title=settings.FCM_APP_NAME,
                            body=content[0:256] if content else "Receive an image",
                            image=None,
                        ),
                        apns=_messaging_utils.APNSConfig(
                            payload=APNSPayload(aps=Aps(badge=1, sound="default"))
                        ),
                        token=push_id,
                    )
                    batch_messages.append(message)

                # 发送批量消息
                response = firebase_admin.messaging.send_each(batch_messages, app=fcm_app)

                # 处理批次结果
                for i, resp in enumerate(response.responses):
                    push_id = batch[i]
                    if resp.success:
                        result_stats["success"] += 1
                    else:
                        result_stats["failed"] += 1
                        error_msg = f"push_id:{push_id[:10]}... failed: {resp.exception}"
                        result_stats["errors"].append(error_msg)
                        logger.error(error_msg)

        except Exception as e:
            logger.error(f"[PushUtil.batch_notify_by_fcm] batch process failed: {str(e)}", exc_info=True)
            result_stats["errors"].append(f"Batch processing error: {str(e)}")

        logger.error(f"[PushUtil.batch_notify_by_fcm] batch process done, result_stats: {result_stats}")
        return result_stats
