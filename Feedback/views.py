"""
Created on 2021/7/4

@author: jon
"""
import datetime
import os
import re

from django.utils.decorators import method_decorator

from AICustomerService.ai_customer_service import AiCustomerServiceUtil
from Common.err import ErrInfo
from Common.ratelimit.decorators import ratelimit
from Common.views import CommonView
from Feedback.models import UserFeedback, UserTicket
from Lamp import settings
from Lamp.settings import logger
from Sms.tools.tool_image import ImageUtil
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_sms import SmsTool
from User.tools.user_tool import UserTool


class SubmitFeedback(CommonView):

    # POST /feedback/submit/
    @CommonView.VerifySign
    def post(self, request):
        try:
            data, err_code, err_msg = self.ReadPostJson(request, ('email', 'content'))
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            ticket_content = data['content']
            feed = UserFeedback(email=data['email'], content=ticket_content)
            feed.save()

            header = self.GetHeaderInRequest(request)
            device_id = header['device_id']
            data['email'] = data['email']
            data['feedback_id'] = feed.id
            data['device_id'] = device_id

            logger.error(f"user feedback, device_id:{device_id}, feedback: {data}")

            # 顺手更新到工单中
            user = UserTool.get_user_by_device_id(device_id)
            if user:
                user_id = user.id
                user_ticket = UserTicket(ticket_type="SubmitFeedback",
                                         user_id=user_id,
                                         device_id=device_id,
                                         email=data['email'],
                                         ticket=ticket_content,
                                         username="",
                                         image_url=""
                                         )
                user_ticket.save()

            # 顺手插入到客服中心中
            number = SmsTool.get_mock_number_by_device_id(device_id)
            SmsItSupportTool.add_support_sms(device_id, settings.SMS_DIRECTION_SEND,
                                             number, settings.APP_IT_SUPPORT_SHOW_PHONE, ticket_content, '')

            # 并给出AI回答
            answer = AiCustomerServiceUtil.get_ai_answer(ticket_content)
            if answer:
                if "{{TICKET_URL}}" in answer:
                    link = f"{settings.STATIC_URL_BASE}/submit_ticket/index.html?wxid={device_id}"
                    answer = answer.replace("{{TICKET_URL}}", link)
                if "[TICKET_URL]" in answer:
                    link = f"{settings.STATIC_URL_BASE}/submit_ticket/index.html?wxid={device_id}"
                    answer = answer.replace("[TICKET_URL]", link)

                # ask
                SmsItSupportTool.add_support_sms_both_from_feedback(device_id, number, ticket_content)
                # answer
                SmsItSupportTool.add_support_sms_both_from_it_with_link(to_device_id=device_id, to_number=number,
                                                                        content=answer, link="")
            else:
                logger.error(f"[SubmitFeedback] get ai answer error, content:{ticket_content}")

            return self.ReturnSuccess()
        except Exception:
            logger.error(f"SubmitFeedback failed", exc_info=True)
            return self.ReturnError(ErrInfo.SERVER_ERROR)


class SubmitUserTicket(CommonView):

    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='20/60s', block=True))
    @method_decorator(ratelimit(key='post:wxid', rate='20/60s', block=True))
    def post(self, request):
        logger.info(f"[SubmitUserTicket] data: {request.POST}")
        ticket_type = request.POST.get('ticket_type')
        username = request.POST.get('username')
        email = request.POST.get('email')
        ticket = request.POST.get('ticket')
        wxid = request.POST.get('wxid')
        wxid = re.sub(r'^\W+|\W+$', '', wxid)

        # 参数校验
        if not ticket_type or len(ticket_type) > 128:
            logger.error(f"[SubmitUserTicket] invalid param, ticket_type, {ticket_type}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")
        if not username or len(username) > 128:
            logger.error(f"[SubmitUserTicket] invalid param, username, {username}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")
        if not email or len(email) > 128:
            logger.error(f"[SubmitUserTicket] invalid param, email, {email}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")
        if not ticket or len(ticket) > 10086:
            logger.error(f"[SubmitUserTicket] invalid param, ticket, {ticket}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")
        if not wxid or len(wxid) > 64:
            logger.error(f"[SubmitUserTicket] invalid param, device_id, {wxid}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")

        # save pic
        today = datetime.datetime.today().strftime("%Y-%m-%d")
        file_dir = settings.BASE_DIR + "/%s/uploads/%s" % ('static', today)
        if not os.path.exists(file_dir):
            os.makedirs(file_dir)

        image_urls = []
        images = request.FILES.getlist('images')
        for image in images:
            filename = f"ticket_{ImageUtil.gen_random_filename()}.png"
            filename_abs = "%s/%s" % (file_dir, filename)
            logger.info(f"save image: {filename_abs}")

            with open(filename_abs, 'wb') as file:
                for chunk in image.chunks():
                    file.write(chunk)

            filename_base = "/uploads/%s/%s" % (today, filename)
            image_urls.append(settings.STATIC_URL_BASE + filename_base)

        image_url = ",".join(image_urls)
        user = UserTool.get_user_by_device_id(wxid)
        if not user:
            logger.error(f"[SubmitUserTicket] user submit ticket, user deviceid invalid: {wxid}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

        user_id = user.id
        user_ticket = UserTicket(ticket_type=ticket_type,
                                 user_id=user_id,
                                 device_id=wxid,
                                 email=email,
                                 ticket=ticket,
                                 username=username,
                                 image_url=image_url
                                 )
        user_ticket.save()

        # 顺手插入到客服中心中
        number = SmsTool.get_mock_number_by_device_id(wxid)
        SmsItSupportTool.add_support_sms(wxid, settings.SMS_DIRECTION_SEND,
                                         number, settings.APP_IT_SUPPORT_SHOW_PHONE, ticket, image_url)

        # 并给出AI回答
        answer = AiCustomerServiceUtil.get_ai_answer(ticket)
        if answer:
            if "{{TICKET_URL}}" in answer:
                link = f"{settings.STATIC_URL_BASE}/submit_ticket/index.html?wxid={wxid}"
                answer = answer.replace("{{TICKET_URL}}", link)
            if "[TICKET_URL]" in answer:
                link = f"{settings.STATIC_URL_BASE}/submit_ticket/index.html?wxid={wxid}"
                answer = answer.replace("[TICKET_URL]", link)
            SmsItSupportTool.add_support_sms_both_from_it_with_link(to_device_id=wxid, to_number=number,
                                                                    content=answer, link="")
        else:
            logger.error(f"[SubmitUserTicket] get ai answer error, content:{ticket}")

        logger.warning(f"[SubmitUserTicket] user submit ticket, device_id:{wxid}, name:{username}, "
                       f"email: {email}, ticket:{ticket}, image_url:{image_url}.")
        return self.ReturnSuccess()
