{
    "inbounds": [
      {
        "port": ${port},
        "listen": "127.0.0.1",
        "protocol": "http",
        "settings": {
          "auth": "noauth",
          "udp": false
        }
      }
    ],
    "outbounds": [
      {
        "protocol": "vmess",
        "settings": {
          "vnext": [
            {
              "address": "${ip}",
              "port": 443,
              "users": [
                {
                  "id": "BF58DDB2-F0CE-479B-BA0E-C77AB65087CD",
                  "alterId": 32
                }
              ]
            }
          ]
        },
        "streamSettings": {
          "tlsSettings": {
            "serverName": "api.gongbaojiding.xyz"
          },
          "network": "ws",
          "security": "tls",
          "skip-cert-verify": true,
          "wsSettings": {
            "path": "/user"
            }
          }
      }
    ]
  }