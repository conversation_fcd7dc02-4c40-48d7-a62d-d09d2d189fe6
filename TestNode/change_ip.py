from datetime import datetime, timedelta
from VPS.vultr import which_account, delete_node_and_server, create_instance_with_hostname
from .test_node import get_server_info
import time
import sys

class ChangeIP():

    def __init__(self):

        self.DAYS_INTERVAL = 3

    def execute(self, hostname = None):

        if hostname is None:
            hostname, account = self.__choose_one()

        if hostname is None:
            print('[change_ip] host is None, get None from __choose_one')
            return
        
        delete_node_and_server(hostname)

        time.sleep(15)
        # v-us-atl-201-**********
        country = hostname.split('-')[1]
        sn = str(hostname.split('-')[3])

        hostname_new = '-'.join(['v', country, sn])  # use v-us-201 to change DC
        create_instance_with_hostname(hostname_new)


    # return hostname, account
    def __choose_one(self):

        for server in get_server_info():
            version = server['version']
            try:
                account = which_account(server['hostname'])
                dt = datetime.strptime(version, '%Y%m%d%H')
                if datetime.now() - dt > timedelta(days=self.DAYS_INTERVAL) and account is not None:
                    print(f"choose: {server}, account={account}")
                    return server['hostname'], account
            except Exception as e:
                print('skip, hostname:', server['hostname'], e)
                continue

        return None, None


if __name__ == '__main__':

    '''
    usage:
        python3 change_ip.py
        python3 change_ip.py node1_name node2_name
    '''

    target_nodes = None
    args = sys.argv
    if len(args) > 1:
        target_nodes = args[1:]
    
    target_nodes = None
    args = sys.argv
    if len(args) > 1:
        target_nodes = args[1:]

    changer = ChangeIP()

    print('-' * 20, 'change ip', '-' * 20, '\t', datetime.now())
    if target_nodes is None:
        changer.execute()
    else:
        for node in target_nodes:
            changer.execute(node)
    
