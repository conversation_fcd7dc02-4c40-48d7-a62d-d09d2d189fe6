from .test_node import get_server_info
from VPS.vultr import Vultr, ACCOUNT_INFO



if __name__ == '__main__':

    v = Vultr(ACCOUNT_INFO['JJ'])
    vultr_list = []
    for i in v.get_all_instances():
        # print(i['main_ip'], i['hostname'])
        if i['hostname'].find('v-') > -1:
            vultr_list.append(i['main_ip'])

    print('vultr size:', len(vultr_list))
    ss_list = []

    # print(s)
    for i in get_server_info():
        ss_list.append(i['ip'])
    

    for s in vultr_list:
        if s not in ss_list:
            print(s)