import time
from mako.template import Template
import subprocess
import os
import sys
import signal
import requests
from datetime import datetime, timedelta
import threading
import queue

"""
install vray
bash <(curl -L https://raw.githubusercontent.com/v2fly/fhs-install-v2ray/master/install-release.sh)

1. update config, config IP and port, eg: ************* 443
2. update nginx port and render client.tt
3. start:  v2ray run -config ./client_config.json
4. test: curl cip.cc -x localhost:10800
"""

Q = queue.Queue(maxsize=1000)

class FoClient:

    def __init__(self, port, thread_id):
        self.port = port
        self.thread_id = thread_id
        self.config_file = 'client_config_%s.json' % self.thread_id
        self.PROXIES = {'http':'http://localhost:%s' % self.port, 'https': 'http://localhost:%s' % self.port}
        self.HEADERS = {
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        }


    def gen_client_config(self, ip):

        print('using config:', self.config_file)
        with open(self.config_file, 'w') as f:
            f.write(Template(filename='./client_config.tt').render(port=self.port, ip=ip))

    def delete_client_config(self):
        if os.path.exists(self.config_file):
            os.unlink(self.config_file)

    def start_ray(self):
        cmd = ['/usr/local/bin/v2ray', 'run', '-config', self.config_file]
        process = subprocess.Popen(cmd, shell=False, stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
        time.sleep(3)
        print("vray started")
        return process.pid


    def stop_ray(self, pid):
        try:
            os.kill(pid, signal.SIGKILL)
            print("vray exited")
        except Exception as e:
            print("vray exited error:", e)

    def check_ip(self, ip):

        url = 'https://ipinfo.io/what-is-my-ip'
        try:
            r = requests.get(url, proxies=self.PROXIES, timeout=15)
            if ip in r.text:
                print('ip check success')
                return True
            else:
                print('ip check failed')
                return False
        except Exception as e:
            print('p check failed:', e)
            return False


    def check_websites(self):

        websites = [
            {"site": "youtube", "url":"https://m.youtube.com/watch?v=k85mRPqvMbE", "keyword": "Crazy Frog"},
            {"site": "tiktok", "url":"https://www.tiktok.com/@tatemcrae?lang=en", "keyword": "tate mcrae"},
            {"site": "instagram", "url":"https://www.instagram.com/explore/", "keyword": "Discover something"},
            {"site": "facebook", "url":"https://m.facebook.com/", "keyword": "Facebook - log in or sign up"},
            {"site": "telegram", "url":"https://t.me/s/Tesla_share/", "keyword": "tesla特斯拉"},
        ]

        res = {}
        for w in websites:
            for _ in range(3):
                try:
                    site = w['site']
                    r = requests.get(w['url'], headers=self.HEADERS, proxies=self.PROXIES, timeout=15)
                    if w['keyword'] in r.text:
                        print(site, 'check success')
                        res[site] = 'success'
                        break
                    else:
                        print(site, 'check failed')
                except Exception as e:
                    print(site, 'check failed:', e)                

                res[site] = 'failed'
                time.sleep(2)
        
        return res



class GetNodeInfo:
    def __init__(self):
        self.list_url = 'https://api.gongbaojiding.xyz/lpapi/server/list/'
        self.report_url = 'https://api.gongbaojiding.xyz/lpapi/config/receiveServerReport/'


    def get_server_info(self):
        try:
            r = requests.post(self.list_url, json={'token': 'XiYang$YangMEiYY$'})
            info = r.json()['data']['servers']
            # print("servers:", info)
            return info
        except Exception as e:
            print('get server info failed:', e)
            return []
    

    def report_server_info(self, info):
        
        res = requests.post(self.report_url, json=info)
        print(res.text)
        print("report sucess")


    def get_ips_all(self):
        ips = []
        for s in self.get_server_info():
            ips.append({"hostname": s['hostname'], "ip": s['ip'], "country": s['country']})
        return ips


    def get_ips_from_hostnames(self, hostnames: list):

        ips = []
        for s in self.get_server_info():
            if s['hostname'] in hostnames:
                ips.append({"hostname": s['hostname'], "ip": s['ip'], "country": s['country']})
        return ips


    def get_ips_within_created_at_less_than(self, hour_number):
        ips = []
        for s in self.get_server_info():
            if datetime.utcnow() - datetime.strptime(s['created_at'], "%Y-%m-%d %H:%M:%S") < timedelta(hours=hour_number):
                ips.append({"hostname": s['hostname'], "ip": s['ip'], "country": s['country']})
        return ips



def check_single_ip(ip, info, port, thread_id):

    fo_client = FoClient(port=port, thread_id=thread_id)

    for _ in range(3):
        fo_client.gen_client_config(ip)
        try:
            v_pid = fo_client.start_ray()
            info['test_result'] = fo_client.check_ip(ip)
        except Exception as e:
            print("run error:", e)

        if info['test_result'] is True:
            info['extra_info']['websites'] = fo_client.check_websites()
            fo_client.stop_ray(v_pid)
            break
        else:
            info['extra_info']['websites'] = {}
            fo_client.stop_ray(v_pid)
            time.sleep(2)
            continue
    
    fo_client.delete_client_config()
    return info


def worker(port, thread_id):

    while Q.qsize() > 0:
        print("worker thread:", thread_id, "qsize:", Q.qsize())
        node_info = Q.get(timeout=1)
        print("get node:", node_info)
        ip = node_info['ip']
        print('-' * 20, 'checking ', node_info, '\t', datetime.now())
        node_info = check_single_ip(ip, node_info, port, thread_id)
        Q.task_done()
        print(node_info)
        GetNodeInfo().report_server_info([node_info])



if __name__ == '__main__':

    '''
    usage:
        python3 test_node.py
        python3 test_node.py node1_name node2_name
        python3 test_node.py new   # new server created within 1h
    '''

    target_nodes = None
    args = sys.argv
    nodes = []
    thread_number = 4
    

    if len(args) == 1:
        print("check all nodes")
        nodes = GetNodeInfo().get_ips_all()
    if len(args) > 1:
        if args[1].isdigit():
            thread_number = int(args[1])
            print("multi thread number:", thread_number)
            nodes = GetNodeInfo().get_ips_all()
        elif args[1].lower() == 'new':
            print("check new nodes")
            nodes = GetNodeInfo().get_ips_within_created_at_less_than(1)
        else:
            print("check hostnames nodes:", args[1:])
            nodes = GetNodeInfo().get_ips_from_hostnames(args[1:])

    for node in nodes:
        t = {'ip': node['ip'], 'extra_info': {"hostname": node['hostname'], "country": node['country']}} 
        Q.put(t)

    
    threads = []
    port_begin = 10800
    for i in range(thread_number):
        t = threading.Thread(target=worker, args=(port_begin + i, i))
        t.start()
        threads.append(t)

    for t in threads:
        t.join()

    print("Test All Done")
