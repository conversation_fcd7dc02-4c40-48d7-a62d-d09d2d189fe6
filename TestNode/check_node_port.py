import requests
import sys
import time
from datetime import datetime

# get [{ip, hostname, country...}]
def get_server_info():
    url = 'https://api.gongbaojiding.xyz/lpapi/server/list/'
    try:
        r = requests.post(url, json={'token': 'XiYang$YangMEiYY$'})
        info = r.json()['data']['servers']
        # print("servers:", info)
        return info
    except Exception as e:
        print('get server info failed:', e)
        return []
    

def check_node_health(ip):
    for _ in range(3):
        try:
            url = 'http://%s:8123/health/' % ip
            r = requests.get(url, timeout=15)
            if r.status_code == 200:
                return True
        except Exception as e:
            print('check node = %s failed:' % ip, e)

        time.sleep(1)

    return False


def report_server_info(info):
    url = 'https://api.gongbaojiding.xyz/lpapi/config/receiveServerReport/'
    res = requests.post(url, json=info)
    print(res.text)
    print("report sucess")


if __name__ == '__main__':

    '''
    usage:
        python3 check_node_port.py
        python3 check_node_port.py node1_name node2_name
    '''

    target_nodes = None
    args = sys.argv
    if len(args) > 1:
        target_nodes = args[1:]
    
    target_nodes = None
    args = sys.argv
    if len(args) > 1:
        target_nodes = args[1:]


    server_info = get_server_info()

    res = []

    for server in server_info:

        if target_nodes is not None and server['hostname'] not in target_nodes:
            continue

        ip = server['ip']
        t = {'ip': ip, 'extra_info': {"hostname": server['hostname'], "country": server['country']}} 
        print('-' * 20, 'checking ', ip, '-' * 20, server['hostname'], '\t', datetime.now())
        t['test_result'] = check_node_health(ip)
        print(t)
        res.append(t)

    if target_nodes is None:
        report_server_info(res)
