import time
from mako.template import Template
import subprocess
import os
import sys
import signal
import requests
from datetime import datetime, timedelta

"""
install vray
bash <(curl -L https://raw.githubusercontent.com/v2fly/fhs-install-v2ray/master/install-release.sh)

1. update config, config IP and port, eg: ************* 443
2. update nginx port and render client.tt
3. start:  v2ray run -config ./client_config.json
4. test: curl cip.cc -x localhost:10800
"""
PORT = 10800

HEADERS = {
    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
}

PROXIES = {'http':'http://localhost:%s' % PORT, 'https': 'http://localhost:%s' % PORT}


def gen_client_config(port, ip):

    print('using port:', port)
    with open('client_config.json', 'w') as f:
        f.write(Template(filename='./client_config.tt').render(port=port, ip=ip))


def start_ray():
    cmd = ['/usr/local/bin/v2ray', 'run', '-config', './client_config.json']
    process = subprocess.Popen(cmd, shell=False)
    time.sleep(3)
    print("vray started")
    return process.pid


def stop_ray(pid):
    try:
        os.kill(pid, signal.SIGKILL)
        print("vray exited")
    except Exception as e:
        print("vray exited error:", e)

def check_ip(ip):

    # url = 'http://cip.cc'
    url = 'https://ipinfo.io/what-is-my-ip'
    try:
        r = requests.get(url, proxies=PROXIES, timeout=15)
        if ip in r.text:
            print('ip check success')
            return True
        else:
            print('ip check failed')
            return False
    except Exception as e:
        print('p check failed:', e)
        return False


def check_websites():

    websites = [
        {"site": "youtube", "url":"https://m.youtube.com/watch?v=k85mRPqvMbE", "keyword": "Crazy Frog"},
        {"site": "tiktok", "url":"https://www.tiktok.com/@tatemcrae?lang=en", "keyword": "tate mcrae"},
        {"site": "instagram", "url":"https://www.instagram.com/explore/", "keyword": "Discover something"},
        {"site": "facebook", "url":"https://m.facebook.com/", "keyword": "Facebook - log in or sign up"},
        {"site": "telegram", "url":"https://t.me/s/Tesla_share/", "keyword": "tesla特斯拉"},
        # {"site": "twitter", "url":"https://x.com/tesla", "keyword": "Electric vehicles"},
    ]

    res = {}
    for w in websites:
        for _ in range(3):
            try:
                site = w['site']
                r = requests.get(w['url'], headers=HEADERS, proxies=PROXIES, timeout=15)
                if w['keyword'] in r.text:
                    print(site, 'check success')
                    res[site] = 'success'
                    break
                else:
                    print(site, 'check failed')
            except Exception as e:
                print(site, 'check failed:', e)                

            res[site] = 'failed'
            time.sleep(2)
    
    return res


def get_server_info():
    url = 'https://api.gongbaojiding.xyz/lpapi/server/list/'
    try:
        r = requests.post(url, json={'token': 'XiYang$YangMEiYY$'})
        info = r.json()['data']['servers']
        # print("servers:", info)
        return info
    except Exception as e:
        print('get server info failed:', e)
        return []
    

def report_server_info(info):
    url = 'https://api.gongbaojiding.xyz/lpapi/config/receiveServerReport/'
    res = requests.post(url, json=info)
    print(res.text)
    print("report sucess")


def check_single_ip(ip, info):
    for _ in range(3):
        gen_client_config(PORT, ip)
        try:
            v_pid = start_ray()
            info['test_result'] = check_ip(ip)
        except Exception as e:
            print("run error:", e)

        if info['test_result'] is True:
            info['extra_info']['websites'] = check_websites()
            stop_ray(v_pid)
            break
        else:
            info['extra_info']['websites'] = {}
            stop_ray(v_pid)
            time.sleep(2)
            continue
    return info

def get_ips_all():
    ips = []
    for s in get_server_info():
        ips.append({"hostname": s['hostname'], "ip": s['ip'], "country": s['country']})
    return ips


def get_ips_from_hostnames(hostnames: list):

    ips = []
    for s in get_server_info():
        if s['hostname'] in hostnames:
            ips.append({"hostname": s['hostname'], "ip": s['ip'], "country": s['country']})
    return ips

def get_ips_using_id_type(id_type):
    ips = []
    for s in get_server_info():
        if id_type.lower() == 'even' and s['id'] % 2 == 0:
             ips.append({"hostname": s['hostname'], "ip": s['ip'], "country": s['country']})
        elif id_type.lower() == 'odd' and s['id'] % 2 == 1:
            ips.append({"hostname": s['hostname'], "ip": s['ip'], "country": s['country']})
    return ips


def get_ips_within_created_at_less_than(hour_number):
    ips = []
    for s in get_server_info():
        if datetime.utcnow() - datetime.strptime(s['created_at'], "%Y-%m-%d %H:%M:%S") < timedelta(hours=hour_number):
            ips.append({"hostname": s['hostname'], "ip": s['ip'], "country": s['country']})
    return ips


if __name__ == '__main__':

    '''
    usage:
        python3 test_node.py
        python3 test_node.py node1_name node2_name
        python3 test_node.py odd | even  # server id odd or even
        python3 test_node.py new   # new server created within 1h
    '''

    target_nodes = None
    args = sys.argv
    nodes = []

    if len(args) == 1:
        print("check all nodes")
        nodes = get_ips_all()
    if len(args) > 1:
        if args[1].lower() == 'new':
            print("check new nodes")
            nodes = get_ips_within_created_at_less_than(2)
        elif args[1].lower() == 'odd' or args[1].lower() == 'even':
            print("check id type nodes:", args[1].lower())
            nodes = get_ips_using_id_type(args[1])
        else:
            print("check hostnames nodes:", args[1:])
            nodes = get_ips_from_hostnames(args[1:])

    for node in nodes:

        ip = node['ip']
        t = {'ip': ip, 'extra_info': {"hostname": node['hostname'], "country": node['country']}} 
        print('-' * 20, 'checking ', ip, '-' * 20, node['hostname'], '\t', datetime.now())

        t = check_single_ip(ip, t)
        print(t)
        report_server_info([t])
    
    print("Test All Done")
