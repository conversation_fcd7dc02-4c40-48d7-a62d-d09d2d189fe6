import base64
import os
import random
import string
import time
from datetime import datetime

from Lamp import settings
from Lamp.settings import logger


class ImageUtil:

    @staticmethod
    def gen_random_filename():
        now = int(time.time())
        random_str = ''.join(random.sample(string.ascii_letters + string.digits, 8))
        return str(now) + '_' + random_str

    @staticmethod
    def save_image(file_data, file_format):
        try:
            today = datetime.today().strftime("%Y-%m-%d")
            file_dir = settings.BASE_DIR + "/%s/uploads/%s" % ('static', today)
            if not os.path.exists(file_dir):
                os.makedirs(file_dir)

            filename = "%s.%s" % (ImageUtil.gen_random_filename(), file_format)
            filename_abs = "%s/%s" % (file_dir, filename)
            logger.info(f"[SendSms] image: {filename_abs}")

            f = open(filename_abs, 'wb')
            f.write(base64.b64decode(file_data))
            f.close()

            filename_base = "/uploads/%s/%s" % (today, filename)
            return settings.STATIC_URL_BASE + filename_base
        except Exception:
            logger.error('[SendSms] error: ', exc_info=True)
            return None
