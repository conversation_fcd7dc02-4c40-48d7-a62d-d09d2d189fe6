from django.core.cache import cache

from Common.err import ErrInfo
from Common.rediskey import RedisKey
from Common.timeutil import TimeUtil
from Common.util import Util
from Common.views import CommonView
from Lamp import settings
from Lamp.settings import logger
from Sms.tools.tool_image import ImageUtil
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_sms import SmsTool
from User.tools.user_tool import UserTool


class CustomerServiceSendSMS(CommonView):
    @CommonView.VerifySign
    def post(self, request):
        post_data, err_code, err_msg = self.ReadPostJson(request, check_list=['content', ])
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        header = self.GetHeaderInRequest(request)
        from_device_id = header['device_id']

        try:
            from_number = SmsTool.get_mock_number_by_device_id(from_device_id)
            to_number = Util.format_number(settings.APP_IT_SUPPORT_SHOW_PHONE)
            latest_ts = post_data.get('latest_ts', TimeUtil.GetNowTsInStr())
            content = (post_data['content'].replace("\n", " ").replace("\r", " ").replace("\r\n", " ")
                       .replace("\t", " ").replace("\f", " ").replace("\v", " ").strip())
            logger.warning(f"[SendSms] device: {from_device_id}, content:{content}")

            # support 模式
            SmsItSupportTool.it_support(from_device_id, from_number, to_number, content, latest_ts)
            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[SendSms] device:{from_device_id}, failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class CustomerServiceSendMMS(CommonView):

    @CommonView.VerifySign
    def post(self, request):

        post_data, err_code, err_msg = self.ReadPostJson(request, ('content', 'format',))
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        header = self.GetHeaderInRequest(request)
        from_device_id = header['device_id']

        try:
            from_number = SmsTool.get_mock_number_by_device_id(from_device_id)
            to_number = settings.APP_IT_SUPPORT_SHOW_PHONE
            latest_ts = post_data.get('latest_ts', TimeUtil.GetNowTsInStr())
            logger.info(f"[SendMMS] from_user={from_device_id}, to_phone={to_number}, latest_ts={latest_ts}")

            # 处理图片
            image_url = ImageUtil.save_image(post_data['content'], post_data['format'])
            if not image_url:
                logger.error(f"[SendMMS] device:{from_device_id}, image_url is invalid, can not send mms!")
                return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)
            else:
                logger.warning(f"[SendMMS] device:{from_device_id}, image_url: {image_url}")

            # 判断是否是 support 模式
            SmsItSupportTool.mms_it_support(from_device_id, from_number, to_number, image_url, latest_ts)
            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[SendMMS] device:{from_device_id}, failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class CustomerServiceGetLatestSms(CommonView):

    @CommonView.VerifySign
    def post(self, request):

        post_data, err_code, err_msg = self.ReadPostJson(request, ('latest_ts',))
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        header = self.GetHeaderInRequest(request)
        device_id = header['device_id']
        latest_ts_ = post_data['latest_ts']
        latest_ts_ = int(latest_ts_) if latest_ts_ else 0  # 格式化int

        try:
            # 拿到用户上次的 sms_ts
            user = UserTool.get_user_by_device_id(device_id=device_id)
            before_latest_sms_ts = user.latest_sms_ts if user else 0

            # 取最大的max
            latest_ts_ = max(latest_ts_, before_latest_sms_ts)

            # 拉数据
            records = SmsTool.get_latest_sms_record(device_id, latest_ts_)
            # logger.info(f"[GetLatestSms] device_id:{device_id}, latest_ts:{latest_ts_}, size: {len(records)}")

            # 更新下这个用户的最新 latest_ts
            cache.set(RedisKey.GenSmsLatestTimestamp(device_id), latest_ts_,
                      RedisKey.SMS_LATEST_TIMESTAMP_EXPIRE_SECONDS)

            res = []
            for record in records:
                if not record.latest_ts or record.latest_ts == 0:
                    latest_ts = str(TimeUtil.DateTime2Timestamp(record.created_at))
                else:
                    latest_ts = str(record.latest_ts)

                _from = record.from_number
                _to = record.to_number

                if record.direction == settings.SMS_DIRECTION_SEND:
                    status = "delivered"
                else:
                    status = "received"

                res.append({
                    "latest_ts": latest_ts,
                    "content": record.content,
                    "created_at": record.created_at,
                    "from": _from,
                    "to": _to,
                    "direction": record.direction,
                    "status": status,
                    "image": record.images,
                    "direction_type": 1 if record.direction == settings.SMS_DIRECTION_RECEIVE else 2,  # 1收2发
                    "link": record.link,
                })

            # 更新用户最新的sms ts
            latest_sms_ts = latest_ts_
            if len(res) > 0:
                latest_sms_ts = int(res[-1]["latest_ts"])
                UserTool.update_latest_sms_ts(device_id, latest_sms_ts)

            logger.info(
                f"[GetLatestSms] device_id:{device_id}, before_latest_ts:{latest_ts_}, after_latest_ts:{latest_sms_ts}, "
                f"size:{len(res)}")

            return self.ReturnSuccess(data={"list": res})
        except Exception:
            logger.error(f"[GetLatestSms] device_id:{device_id}, latest_ts:{latest_ts_}, failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)
