"""
Created on 2021/7/9

@author: jon
"""

from Common.views import CommonView
from Report.models import OrderRecord


# POST /report/order/ {'productId', 'price', 'countryCode'}
class ReportOrder(CommonView):

    @CommonView.VerifySign
    def post(self, request):
        data, err_code, err_msg = self.ReadPostJson(request, ('productId', 'price', 'countryCode'))
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        headers = self.GetHeaderInRequest(request)
        print(f"report headers={headers}")

        order = OrderRecord(
            product_id=data['productId'],
            price=data['price'],
            country_code=data['countryCode'],
            device_id=headers['device_id']
        )
        order.save()

        return self.ReturnSuccess()
