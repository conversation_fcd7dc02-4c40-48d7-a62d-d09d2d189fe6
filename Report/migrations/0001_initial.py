# Generated by Django 3.2.5 on 2022-10-08 02:39

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='OrderRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_id', models.Char<PERSON>ield(max_length=100, verbose_name='product id')),
                ('price', models.IntegerField(verbose_name='price')),
                ('country_code', models.CharField(default='CNY', max_length=20, verbose_name='country code')),
                ('device_id',
                 models.CharField(blank=True, default=None, max_length=200, verbose_name='device id, not idfa')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
