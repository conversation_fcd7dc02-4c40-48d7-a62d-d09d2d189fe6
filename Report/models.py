"""
Created on 2021/7/9

@author: jon
"""
from django.db import models


class OrderRecord(models.Model):
    product_id = models.CharField("product id", max_length=100)
    price = models.IntegerField("price")  # unit: cent
    country_code = models.CharField("country code", max_length=20, default="CNY")  # CNY, USD

    device_id = models.CharField('device id, not idfa', max_length=200, blank=True, default=None)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
