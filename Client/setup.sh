#!/bin/sh

URL="https://api.gongbaojiding.xyz/gbjd_client_script/"

mkdir -p /data/work/
cd /data/work/

for file in report_status.py setup_cronjob.py sync.py ray_server.py ray_config.tt bottle.conf change_nginx_port.py report_stat.py
do
    /usr/bin/wget ${URL}${file} -O ./$file
done

# setup cron job
/usr/bin/python3 ./setup_cronjob.py
service cron start
crontab /tmp/cron_file


# BBR
echo "net.core.default_qdisc=fq" >> /etc/sysctl.conf
echo "net.ipv4.tcp_congestion_control=bbr" >> /etc/sysctl.conf
# tcp tw
echo "net.ipv4.tcp_syncookies=1" >> /etc/sysctl.conf
echo "net.ipv4.tcp_tw_reuse=1" >> /etc/sysctl.conf
echo "net.ipv4.tcp_fin_timeout=1" >> /etc/sysctl.conf
echo "net.ipv4.tcp_keepalive_time=300" >> /etc/sysctl.conf
echo "net.ipv4.ip_local_port_range=1024 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_tw_buckets=5000" >> /etc/sysctl.conf
echo "net.core.netdev_max_backlog=8192" >> /etc/sysctl.conf
sysctl -p

# supervisor for bottle
cp ./bottle.conf /etc/supervisor/conf.d/
service supervisor restart


# first report
/usr/bin/python3 ./report_status.py

# init nginx
/usr/bin/python3 ./change_nginx_port.py

# reset user list
/usr/bin/curl 'localhost:8123/reset/' -d '{"token":"v-MxkdFATpo-k2_mASaKx_token"}'

# disable auto update
sudo systemctl disable --now unattended-upgrades

# enable swap
fallocate -l 2G /swapfile
chmod 600 /swapfile
mkswap /swapfile 
swapon /swapfile
free -h
