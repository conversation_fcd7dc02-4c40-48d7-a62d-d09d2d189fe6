'''
Created on Oct 24, 2021
@author: jon
'''
import os

version = os.popen('hostname').read().strip().split('-')[0]

file_name = '/tmp/cron_file'

content = '''0 1 * * * echo 3 > /proc/sys/vm/drop_caches
*/2 * * * * cd /data/work && /usr/bin/python3 ./report_status.py >> /tmp/report.log
0 1 * * * cd /data/work && /usr/bin/python3 ./sync.py
1 0 * * * cd /data/work && /usr/bin/python3 ./change_nginx_port.py >> /tmp/change_ng.log
58 23 * * * cd /data/work && /usr/bin/python3 ./report_stat.py >> /tmp/stat.log
'''

f = open(file_name, 'w')
f.write(content)
f.close()

print("write cron file Done")
