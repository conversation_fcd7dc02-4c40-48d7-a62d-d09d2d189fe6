{

  "log": {
        "access": "/var/log/v2ray/access.log",
        "error": "/var/log/v2ray/error.log",
        "loglevel": "info"
    },

  "stats": {},
  "api": {
    "tag": "api",
    "services": [
      "StatsService"
    ]
  },
  "policy": {
    "levels": {
      "0": {
        "statsUserUplink": true,
        "statsUserDownlink": true
      }
    },
    "system": {
      "statsInboundUplink": true,
      "statsInboundDownlink": true,
      "statsOutboundUplink": true,
      "statsOutboundDownlink": true
    }
  },
  "inbounds": [
    {
      "listen": "127.0.0.1",
      "port": 18082,
      "protocol": "vmess",
      "settings": {
            "clients": [
             % for id in ids[:-1]:
                {"email": "${id}", "id": "${id}", "alterId": 32, "level": 0},
             % endfor
                {"email": "${ids[-1]}", "id": "${ids[-1]}", "alterId": 32, "level": 0}
            ]
      },
    "streamSettings": {
            "network": "ws",
            "security": "none",
            "wsSettings": {
                "path": "/user"
            }
      }
    },

    {
      "listen": "127.0.0.1",
      "port": 18085,
      "protocol": "dokodemo-door",
      "settings": {
        "address": "127.0.0.1"
      },
      "tag": "api"
    }
  ],
  "outbounds": [
    {
      "protocol": "freedom",
      "settings": {}
    }
  ],
  "routing": {
    "rules": [
      {
        "inboundTag": [
          "api"
        ],
        "outboundTag": "api",
        "type": "field"
      }
    ],
    "domainStrategy": "AsIs"
  }
}
