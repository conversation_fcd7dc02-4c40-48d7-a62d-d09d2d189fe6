
Depend:

mkdir -p /data/work
mkdir -p /data/web
mkdir -p /data/ssl

apt-get update

apt install v2ray
apt install python3-pip
apt install supervisor
apt install nginx -y
apt install resolvconf

pip3 install requests mako FileLock psutil gunicorn bottle --break-system-packages

#v2ray
bash <(curl -L https://raw.githubusercontent.com/v2fly/fhs-install-v2ray/master/install-release.sh)

# fix v2.service
vi /etc/systemd/system/v2ray.service
add line in [Service] 
    Environment=V2RAY_VMESS_AEAD_FORCED=false

# reload service
systemctl daemon-reload

--- supervisor: /etc/supervisor/conf.d/bottle.conf ----
[program:bottle]
directory=/data/work
command=/usr/bin/python3 -u /usr/local/bin/gunicorn ray_server:app -b 0.0.0.0:8123 -w 1
stderr_logfile=/tmp/bottle_stderr.log
stdout_logfile=/tmp/bottle_stdout.log
redirect_stderr = true



---- nginx: /etc/nginx/sites-enabled/wss ---
server{

  listen 443 ssl;
  server_name api.gongbaojiding.xyz;

  ssl_certificate /data/ssl/fullchain.pem;
  ssl_certificate_key /data/ssl/privkey.pem;

  location / {
    root /data/web;
    index index.html;
    try_files $uri $uri $uri/ =404;
  }

  location /user {
    proxy_redirect off;
    proxy_pass http://localhost:18082;
    proxy_http_version 1.1;
    proxy_set_header Upgrade websocket;
    proxy_set_header Connection Upgrade;
    proxy_set_header Host $http_host;
  }

}


---------- vi /etc/rc.local -------
#!/bin/sh
cd /data/work && /usr/bin/python3 ./sync.py
exit 0


chmod 555 /etc/rc.local

ufw disable

# ---- setup nameserver ----
systemctl start resolvconf.service
echo "nameserver *******" >> /etc/resolvconf/resolv.conf.d/head
echo "nameserver *******" >> /etc/resolvconf/resolv.conf.d/head
sudo resolvconf --enable-updates
sudo resolvconf -u
systemctl restart resolvconf.service
