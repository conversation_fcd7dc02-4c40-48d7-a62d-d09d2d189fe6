import datetime
import os
import time

tt = '''
server{

  listen %s ssl;
  server_name api.gongbaojiding.xyz;

  ssl_certificate /data/ssl/fullchain.pem;
  ssl_certificate_key /data/ssl/privkey.pem;

  location / {
    proxy_ssl_name www.pexels.com;
    proxy_ssl_server_name on;
    proxy_ssl_verify off;
    proxy_pass https://www.pexels.com/;
  }

  location /user {
    proxy_read_timeout 600;
    proxy_send_timeout 600;
    proxy_redirect off;
    proxy_pass http://127.0.0.1:18082;
    proxy_http_version 1.1;
    proxy_set_header Upgrade websocket;
    proxy_set_header Connection Upgrade;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
	proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }
  
}
'''
def get_port():
    days = int(time.time()/3600/24)
    port = abs(days - int(str(days)[::-1]) * 2)
    if port < 5000:
        port = 5000 + port
    elif port > 50000:
        port = int(port / 4)
    return port



if __name__ == '__main__':
    # p = get_port()
    p = 443
    with open('/etc/nginx/sites-enabled/wss', 'w') as f:
        f.write(tt % p)
        print(str(datetime.datetime.now()), "-> change port to:", p)

    os.popen('/usr/sbin/service nginx restart')




