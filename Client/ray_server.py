"""
Created on 2024/3/29

@author: jon
"""
import json
import os
from mako.template import Template
from bottle import route, run, default_app, request
from filelock import FileLock
from datetime import datetime
import time


ACCESS_TOKEN = 'v-MxkdFATpo-k2_mASaKx_token'
CONFIG_FILE = "/usr/local/etc/v2ray/config.json"
# CONFIG_FILE = "/tmp/config.json"
LOCK_FILE = '/tmp/config.lock'


def __update_config(ids):

    lock = FileLock(LOCK_FILE, timeout=3)
    with lock:
        with open(CONFIG_FILE, 'w') as f:
            f.write(Template(filename='./ray_config.tt').render(ids=ids))


# return user ids
def __load_user_id_from_config():
    if not os.path.exists(CONFIG_FILE):
        return []
    try:
        j = json.load(open(CONFIG_FILE))
        clients = j['inbounds'][0]['settings']['clients']
        return [user['id'] for user in clients]
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] load config error:', str(e))
        return []



def __add_users(user_ids):
    updated = 0
    exist_user_ids = __load_user_id_from_config()
    for id in user_ids:
        if id not in exist_user_ids:
            exist_user_ids.append(id)
            updated += 1
    __update_config(exist_user_ids)
    return updated


def __del_users(user_ids):
    updated = 0
    exist_user_ids = __load_user_id_from_config()
    for id in user_ids:
        if id in exist_user_ids:
            exist_user_ids.remove(id)
            updated += 1
    __update_config(exist_user_ids)
    return updated


def __verify_token(data):
    token = data.get('token', None)
    return True if token == ACCESS_TOKEN else False

def __service_restart():
    __save_stat()
    cmd = '/usr/sbin/service v2ray restart'
    os.popen(cmd).read()


def __save_stat():
    CMD = '/usr/local/bin/v2ray api stats --server=127.0.0.1:18085'
    date_str = time.strftime('%Y%m%d')
    file_base = '/data/work/stat/'
    os.makedirs(file_base, exist_ok=True)
    file_path = os.path.join(file_base, f'{date_str}.txt')

    with open(file_path, 'a') as f:
        f.write(time.strftime('%Y-%m-%d %H:%M:%S') + '\n')
        f.write(os.popen(CMD).read() + '\n')
    
    print('stat saved: ', file_path)


def __load_data_in_request(request):
    return json.loads(request.body.getvalue())

@route('/user/add/', method='POST')
def AddUser():
    data = __load_data_in_request(request)
    if __verify_token(data) is False:
        return {"error": "miss token or wrong"}

    ids = data.get('ids', [])

    if len(ids) == 0:
        return {"error": "add ids list is empty"}

    updated = __add_users(ids)
    if updated > 0:
        __service_restart()
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] add users: {ids}')
    else:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] add users: {ids}, already exist')

    return {"error_code": 0}



@route('/user/del/', method='POST')
def DelUser():
    data = __load_data_in_request(request)

    if __verify_token(data) is False:
        return {"error": "miss token or wrong"}

    ids = data.get('ids', [])

    if len(ids) == 0:
        return {"error": "del ids list is empty"}

    updated = __del_users(ids)
    if updated > 0:
        __service_restart()
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] del users: {ids}')
    else:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] del users: {ids}, not exist')
    return {"error_code": 0}


@route('/reset/', method='POST')
def Reset():
    data = __load_data_in_request(request)

    if __verify_token(data) is False:
        return {"error": "miss token or wrong"}

    ##set a default ID for testing
    __update_config(('BF58DDB2-F0CE-479B-BA0E-C77AB65087CD',))
    __service_restart()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] reset users')
    return {"error_code": 0}


@route('/health/', method='GET')
def Health():
    return {"error_code": 0}


app = default_app()

if __name__ == '__main__':
    run(server='gunicorn', host='0.0.0.0', port=8123)
    # run(host='0.0.0.0', port=8000)
