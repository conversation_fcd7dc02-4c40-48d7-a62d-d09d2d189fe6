'''
Created on Oct 16, 2021

@author: jon
'''

import json
import os
import random
import socket
import time
import psutil
import requests
from datetime import datetime, timedelta


REPORT_URL = "https://api.gongbaojiding.xyz/lpapi/server/report/"
REPORT_KEY = 'XNsj^Nbsj72jAn-xYwuiUKS_xMMZ'


def get_ex_ip_address():
    if_info = psutil.net_if_addrs()

    if 'eth0' in if_info:
        eth = if_info['eth0']
    if 'enp1s0' in if_info:
        eth = if_info['enp1s0']
    ip_ex = eth[0].address
    print(f"ip_ex = {ip_ex}")

    return ip_ex


def get_host_name():
    return socket.gethostname()


def get_person_online():
    now = datetime.now()
    now_str = now.strftime("%d/%b/%Y:%H:%M")

    now_1 = datetime.now() - timedelta(minutes=1)
    now_1_str = now_1.strftime("%d/%b/%Y:%H:%M")

    cmd = "tail -n 1000 /var/log/nginx/access.log |grep -a '/user' |grep -av '101 0' | grep -a -E '%s|%s'|awk '{print $1}' |sort |uniq |wc -l" \
            % (now_1_str, now_str)

    res = -1    # if -1, check error
    try:
        res = int(os.popen(cmd).read())
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}]report error:', str(e))

    return res


# v-us-atl-201-2025080712
def ReportServerStatus():
    ex_ip = get_ex_ip_address()
    host_name = get_host_name()
    country = host_name.split('-')[1].upper()
    city = host_name.split('-')[2].lower()
    person_online = get_person_online()
    version = host_name.split('-')[-1]

    data = {
        "key": REPORT_KEY,
        "ip": ex_ip,
        "hostname": host_name,
        "online": person_online,
        "country": country,
        "city": city,
        "version": version,
    }

    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}]', data)

    for _ in range(1, 6):

        r = requests.post(REPORT_URL, data=json.dumps(data), timeout=10)
        if r.status_code != 200:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}]not ok, retrying')
        else:
            return

    return


if __name__ == '__main__':
    time.sleep(random.randint(1, 10))
    ReportServerStatus()
