import time

import paramiko
import pymysql
from paramiko.ssh_exception import SSHException
from pymysql.cursors import DictCursor


def get_server_list():
    try:
        connection = pymysql.connect(
            host='127.0.0.1',
            user='root',
            password='123qweQWE',
            database='lamp',
            cursorclass=DictCursor
        )
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM Server_server WHERE enabled = 1")
            servers = cursor.fetchall()
            return servers
    except pymysql.Error as e:
        print(f"数据库连接错误: {e}")
    finally:
        if 'connection' in locals() and connection:
            connection.close()
    return []


def execute_ssh_command(server, command):
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        # 使用密钥认证
        private_key = paramiko.RSAKey.from_private_key_file('/root/.ssh/id_rsa')
        ssh.connect(
            hostname=server['ip'],
            username='root',
            pkey=private_key,
            timeout=10
        )
        stdin, stdout, stderr = ssh.exec_command(command)
        exit_status = stdout.channel.recv_exit_status()
        if exit_status == 0:
            result = stdout.read().decode('utf-8').strip()
            return result
        else:
            error = stderr.read().decode('utf-8')
            print(f"命令执行失败: {error}")
    except SSHException as ssh_ex:
        print(f"SSH连接错误 {server['ip']}: {str(ssh_ex)}")
    except Exception as ex:
        print(f"处理服务器 {server['ip']} 时发生错误: {str(ex)}")
    finally:
        if 'ssh' in locals() and ssh:
            ssh.close()
    return "0"




if __name__ == "__main__":
    commond = 'cd /data/work && wget https://api.gongbaojiding.xyz/gbjd_client_script/report_status.py -q -O ./report_status.py && python3 ./report_status.py'

    servers = get_server_list()
    if not servers:
        print("未找到可用服务器")
        exit("not found servers")

    print("index\tIP\thostname\tcnt")
    for index, server in enumerate(servers):
        cnt = execute_ssh_command(server, commond)
        print(f"{index}\t{server['ip']}\t{server['hostname']}\t{cnt}")
        # 避免请求过于频繁
        time.sleep(1)
