'''
Created on Oct 23, 2021
@author: jon
'''
import json
import os
import random
import time

import requests

url_conf = "https://api.gongbaojiding.xyz/gbjd_client_script/version.json"

ssl_fullchain = 'https://api.gongbaojiding.xyz/gbjd_client_script/ssl/fullchain.pem'
ssl_privekey = 'https://api.gongbaojiding.xyz/gbjd_client_script/ssl/privkey.pem'

# {
#     "version": 1.1,
#     "sh_url": "https://api.gongbaojiding.xyz/gbjd_client_script/setup.sh",
# }


def get_ssl_files():
    try:
        open('/data/ssl/privkey.pem', 'w').write(requests.get(ssl_privekey).text)
        open('/data/ssl/fullchain.pem', 'w').write(requests.get(ssl_fullchain).text)
        os.popen('/usr/sbin/service nginx restart').read()
    except Exception as e:
        print("get ssl file error:" + str(e))
        return


def get_local_version(version_file) -> float:
    try:
        f = open(version_file, 'r')
        version = float(f.read().strip())
        f.close()
        return version
    except Exception:
        return 0


def update_local_version(version_file, latest_version):
    try:
        f = open(version_file, 'w')
        f.write("%s\n" % latest_version)
        f.close()
    except Exception as e:
        print("update local version error:" + str(e))
        return


def is_version_updated(latest_version) -> bool:
    version_file = os.getcwd() + '/local.version'

    local_version = get_local_version(version_file)

    print(f"local_version = {local_version}, latest_version = {latest_version}")

    if latest_version > local_version:
        print("updating...")
        update_local_version(version_file, latest_version)
        return True

    print("no update")
    return False


def get_latest_version(url) -> tuple:
    r = requests.get(url)
    print(r.text)
    j = json.loads(r.text)
    return j['version'], j['sh_url']


if __name__ == '__main__':

    time.sleep(random.randint(1, 5))

    get_ssl_files()

    latest_version, sh_url = get_latest_version(url_conf)

    if is_version_updated(latest_version) is True:
        sh_name = sh_url[sh_url.rfind('/') + 1:]

        sh_ctx = requests.get(sh_url).text

        print(f"{sh_name} --> {sh_ctx}")

        open(f'./{sh_name}', 'w').write(sh_ctx)

        cmd = f'/bin/sh ./{sh_name}'
        print(os.popen(cmd).read())

    print("done")
