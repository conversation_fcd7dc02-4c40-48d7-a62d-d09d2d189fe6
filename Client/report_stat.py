import requests
import time
import re
import os
import socket
import psutil
import random
import sys

REPORT_URL = "https://api.gongbaojiding.xyz/lpapi/config/receiveUserUsageReport/"
REPORT_KEY = '!@cdioshcs12!@DA#!s"'

p = re.compile(r'(.*?)(GB|MB|KB)\s+?user>>>(.*?)>>>traffic')

def get_stat(file_path):
    res = {}
    f = open(file_path, 'r')
    for line in f:
        try:
            if line.find('user') == -1 or line.find('traffic') == -1:
                continue
            line = ' '.join(line.split(' ')[1:]).strip()
            id = None
            traffic = 0
            m = p.match(line)
            if m:
                traffic = float(m.groups()[0])
                unit = m.groups()[1]
                id = m.groups()[2]
            else:
                print('line match None:', line)
                continue

            if unit == 'GB':
                traffic *= 1024
            elif unit == 'KB':
                traffic /= 1024

            if id not in res:
                res[id] = 0

            res[id] += float(traffic)
        except Exception:
            print('line match None:', line)
            continue

        # print(f'\t\tuid={uid}, traffic={traffic}')       

    # for id in res.keys():
    #     print(f'id={id}, traffic={round(res[id], 2)} MB') 
    # {id: total}
    return res


def get_ex_ip_address():
    if_info = psutil.net_if_addrs()

    if 'eth0' in if_info:
        eth = if_info['eth0']
    if 'enp1s0' in if_info:
        eth = if_info['enp1s0']
    ip_ex = eth[0].address
    print(f"ip_ex = {ip_ex}")

    return ip_ex

def get_host_name():
    return socket.gethostname()



if __name__ == '__main__':
    '''
    usage:
        python3 report_stat.py
        python3 report_stat.py 20250821
    '''

    time.sleep(random.randint(1, 10))

    date_str = None
    args = sys.argv
    date_str = time.strftime('%Y%m%d')

    if len(args) > 1:
        date_str = args[1]

    file_base = '/data/work/stat/'
    os.makedirs(file_base, exist_ok=True)
    file_path = os.path.join(file_base, f'{date_str}.txt')

    res = get_stat(file_path)
    traffic = []
    for id in res.keys():
        traffic.append({'id': id, 'total': round(res[id], 2)})
    
    data = {
        'date': date_str,
        'token': REPORT_KEY,
        'ip': get_ex_ip_address(),
        'host': get_host_name(),
        'traffic': traffic
    }

    print(data)
    resp = requests.post(REPORT_URL, json=data)
    print(resp.text)